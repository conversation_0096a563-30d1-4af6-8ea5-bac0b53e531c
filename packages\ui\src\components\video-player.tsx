"use client"

import type React from "react"
import { useRef, useEffect, useState } from "react"

export interface VideoPlayerProps {
  src: string
  poster?: string
  vttSrc?: string
  currentTime?: number
  onTimeUpdate?: (time: number) => void
  className?: string
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  poster,
  vttSrc,
  currentTime,
  onTimeUpdate,
  className = "",
}) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(1)
  const [duration, setDuration] = useState(0)
  const [currentProgress, setCurrentProgress] = useState(0)

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleDurationChange = () => {
      setDuration(video.duration)
    }

    const handleTimeUpdate = () => {
      setCurrentProgress(video.currentTime)
      onTimeUpdate?.(video.currentTime)
    }

    video.addEventListener("durationchange", handleDurationChange)
    video.addEventListener("timeupdate", handleTimeUpdate)

    return () => {
      video.removeEventListener("durationchange", handleDurationChange)
      video.removeEventListener("timeupdate", handleTimeUpdate)
    }
  }, [onTimeUpdate])

  useEffect(() => {
    if (videoRef.current && currentTime !== undefined && !isNaN(currentTime)) {
      videoRef.current.currentTime = currentTime
    }
  }, [currentTime])

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (video.paused) {
      video.play()
      setIsPlaying(true)
    } else {
      video.pause()
      setIsPlaying(false)
    }
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = Number.parseFloat(e.target.value)
    setVolume(newVolume)
    if (videoRef.current) {
      videoRef.current.volume = newVolume
    }
  }

  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTime = Number.parseFloat(e.target.value)
    setCurrentProgress(newTime)
    if (videoRef.current) {
      videoRef.current.currentTime = newTime
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`
  }

  return (
    <div className={`relative ${className}`}>
      <video ref={videoRef} className="w-full rounded-lg" poster={poster} preload="metadata">
        <source src={src} />
        {vttSrc && <track kind="subtitles" src={vttSrc} default />}
      </video>

      <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-2 rounded-b-lg">
        <div className="flex items-center space-x-2">
          <button
            onClick={togglePlay}
            className="text-white p-1 rounded hover:bg-white/20"
            aria-label={isPlaying ? "Pause" : "Play"}
          >
            {isPlaying ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="6" y="4" width="4" height="16"></rect>
                <rect x="14" y="4" width="4" height="16"></rect>
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
            )}
          </button>

          <div className="text-white text-xs">
            {formatTime(currentProgress)} / {formatTime(duration)}
          </div>

          <input
            type="range"
            min="0"
            max={duration || 0}
            value={currentProgress}
            onChange={handleProgressChange}
            className="flex-grow h-1 bg-gray-600 rounded-full appearance-none cursor-pointer"
          />

          <div className="flex items-center space-x-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-white"
            >
              <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
              <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
              <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
            </svg>

            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={volume}
              onChange={handleVolumeChange}
              className="w-16 h-1 bg-gray-600 rounded-full appearance-none cursor-pointer"
            />
          </div>
        </div>
      </div>
    </div>
  )
}
