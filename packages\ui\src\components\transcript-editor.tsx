"use client"

import type React from "react"
import { useState, useEffect } from "react"

export interface Word {
  text: string
  start: number
  end: number
  confidence?: number
}

export interface TranscriptEditorProps {
  words: Word[]
  currentTime?: number
  onWordClick?: (time: number) => void
  className?: string
}

export const TranscriptEditor: React.FC<TranscriptEditorProps> = ({
  words,
  currentTime = 0,
  onWordClick,
  className = "",
}) => {
  const [activeWordIndex, setActiveWordIndex] = useState<number | null>(null)

  useEffect(() => {
    if (currentTime > 0) {
      const index = words.findIndex((word) => currentTime >= word.start && currentTime <= word.end)
      setActiveWordIndex(index !== -1 ? index : null)
    } else {
      setActiveWordIndex(null)
    }
  }, [currentTime, words])

  const handleWordClick = (word: Word, index: number) => {
    setActiveWordIndex(index)
    onWordClick?.(word.start)
  }

  return (
    <div className={`p-4 bg-white rounded-lg shadow ${className}`}>
      <div className="prose max-w-none">
        {words.map((word, index) => (
          <span
            key={`${word.text}-${index}`}
            className={`inline cursor-pointer mx-0.5 px-0.5 rounded ${
              activeWordIndex === index ? "bg-primary text-primary-foreground" : "hover:bg-gray-100"
            }`}
            onClick={() => handleWordClick(word, index)}
          >
            {word.text}
          </span>
        ))}
      </div>
    </div>
  )
}
