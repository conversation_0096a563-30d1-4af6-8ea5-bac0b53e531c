{"version": 2, "buildCommand": "npm run build --filter=web", "outputDirectory": "apps/web/.next", "installCommand": "npm ci", "framework": "nextjs", "regions": ["iad1", "sfo1", "lhr1"], "functions": {"apps/web/src/app/api/**/*.ts": {"maxDuration": 30}, "apps/web/src/app/api/videos/upload/route.ts": {"maxDuration": 300}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/health", "destination": "/api/health"}, {"source": "/metrics", "destination": "/api/metrics"}], "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "HF_API_TOKEN": "@hf_api_token", "WHISPER_MODEL": "@whisper_model", "ASR_BACKEND": "@asr_backend", "LOG_LEVEL": "@log_level", "ANALYZE": "@analyze"}}