import { spawn } from "child_process"
import { v4 as uuidv4 } from "uuid"
import fs from "fs"
import path from "path"
import type { AsrClient, TranscriptionResult } from "./AsrClient"
import { logger } from "../utils/logger"

export class LocalWhisperClient implements AsrClient {
  constructor(private model = "small") {}

  async transcribe(filePath: string, locale?: string): Promise<TranscriptionResult> {
    logger.info({ msg: "Transcribing with Local Whisper", model: this.model, locale })

    try {
      // Create a temporary output file
      const outputPath = path.join(path.dirname(filePath), `${path.basename(filePath, path.extname(filePath))}.json`)

      // Build command arguments
      const args = [
        "-m",
        this.model,
        "--output_format",
        "json",
        "--output_dir",
        path.dirname(outputPath),
        "--word_timestamps",
        "True",
      ]

      if (locale && locale !== "auto") {
        args.push("--language", locale)
      }

      args.push(filePath)

      // Run whisper command
      await new Promise<void>((resolve, reject) => {
        const whisperProcess = spawn("whisper", args)

        whisperProcess.stdout.on("data", (data) => {
          logger.debug(`whisper stdout: ${data}`)
        })

        whisperProcess.stderr.on("data", (data) => {
          logger.debug(`whisper stderr: ${data}`)
        })

        whisperProcess.on("close", (code) => {
          if (code === 0) {
            resolve()
          } else {
            reject(new Error(`whisper process exited with code ${code}`))
          }
        })

        whisperProcess.on("error", (err) => {
          reject(err)
        })
      })

      // Read the output file
      const result = JSON.parse(fs.readFileSync(outputPath, "utf-8"))

      // Transform the result to our standard format
      const transcriptionId = uuidv4()
      const words = result.segments.flatMap((segment: any) =>
        segment.words.map((word: any) => ({
          text: word.text,
          start: Math.round(word.start * 1000), // convert to ms
          end: Math.round(word.end * 1000), // convert to ms
          confidence: word.confidence || 1.0,
        })),
      )

      return {
        id: transcriptionId,
        text: result.text,
        language: result.language || locale || "en",
        words,
        raw: result,
        duration: result.duration * 1000, // convert to ms
      }
    } catch (error) {
      logger.error({ msg: "Local Whisper transcription failed", error })
      throw error
    }
  }
}
