import { type NextRequest, NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Check if user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if video exists and belongs to user
    const { data: video, error: videoError } = await supabase
      .from("videos")
      .select("*")
      .eq("id", params.id)
      .eq("user_id", session.user.id)
      .single()

    if (videoError || !video) {
      return NextResponse.json({ error: "Video not found" }, { status: 404 })
    }

    // Extend TTL by 72 hours from now
    const newExpiryDate = new Date(Date.now() + 72 * 60 * 60 * 1000)

    const { error: updateError } = await supabase
      .from("videos")
      .update({ expires_at: newExpiryDate.toISOString() })
      .eq("id", params.id)

    if (updateError) {
      throw updateError
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error extending TTL:", error)
    return NextResponse.json({ error: "Failed to extend TTL" }, { status: 500 })
  }
}
