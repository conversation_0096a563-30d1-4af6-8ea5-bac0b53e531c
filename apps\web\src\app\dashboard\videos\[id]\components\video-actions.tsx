"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { Button } from "@reality-scripts/ui"
import type { Database } from "@reality-scripts/lib"

interface VideoActionsProps {
  video: {
    id: string
    status: string
    expires_at: string | null
  }
}

export default function VideoActions({ video }: VideoActionsProps) {
  const [isExtending, setIsExtending] = useState(false)
  const [isDownloading, setIsDownloading] = useState<string | null>(null)
  const router = useRouter()
  const supabase = createClientComponentClient<Database>()

  const handleExtendTTL = async () => {
    try {
      setIsExtending(true)

      const response = await fetch(`/api/videos/${video.id}/extend-ttl`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to extend TTL")
      }

      // Refresh the page to show updated expiry time
      router.refresh()
    } catch (error) {
      console.error("Failed to extend TTL:", error)
      alert(error instanceof Error ? error.message : "Failed to extend video retention. Please try again.")
    } finally {
      setIsExtending(false)
    }
  }

  const handleDownload = async (format: string) => {
    try {
      setIsDownloading(format)

      const response = await fetch(`/api/videos/${video.id}/export/${format}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to download ${format.toUpperCase()}`)
      }

      // Create download link
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `transcript.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error(`Failed to download ${format}:`, error)
      alert(error instanceof Error ? error.message : `Failed to download ${format.toUpperCase()}. Please try again.`)
    } finally {
      setIsDownloading(null)
    }
  }

  const isExpiringSoon = video.expires_at && new Date(video.expires_at).getTime() - Date.now() < 12 * 60 * 60 * 1000 // 12 hours

  return (
    <div className="flex flex-wrap gap-2 mt-4">
      {video.expires_at && (
        <Button
          onClick={handleExtendTTL}
          isLoading={isExtending}
          disabled={isExtending}
          variant={isExpiringSoon ? "default" : "outline"}
        >
          {isExtending ? "Extending..." : "Extend Retention (72 hours)"}
        </Button>
      )}

      {video.status === "transcribed" && (
        <>
          <Button
            variant="outline"
            onClick={() => handleDownload("srt")}
            isLoading={isDownloading === "srt"}
            disabled={isDownloading !== null}
          >
            {isDownloading === "srt" ? "Downloading..." : "Download SRT"}
          </Button>
          <Button
            variant="outline"
            onClick={() => handleDownload("vtt")}
            isLoading={isDownloading === "vtt"}
            disabled={isDownloading !== null}
          >
            {isDownloading === "vtt" ? "Downloading..." : "Download VTT"}
          </Button>
          <Button
            variant="outline"
            onClick={() => handleDownload("txt")}
            isLoading={isDownloading === "txt"}
            disabled={isDownloading !== null}
          >
            {isDownloading === "txt" ? "Downloading..." : "Download Text"}
          </Button>
        </>
      )}
    </div>
  )
}
