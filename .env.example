# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=https://yxxabpixkjmzmipxsxpq.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl4eGFicGl4a2ptem1pcHhzeHBxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMjI5NjcsImV4cCI6MjA2MzU5ODk2N30.2_QyZIwQmfumN-w94uqQoilSDj3rVYkhw1JYO-hFN3w
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl4eGFicGl4a2ptem1pcHhzeHBxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAyMjk2NywiZXhwIjoyMDYzNTk4OTY3fQ.SFjfdr0gpjdBJnI32RCo_FlevLMaznBaWgMmPjKytWw
SUPABASE_JWT_SECRET=QVzKS8GK+Q2KW3w78pKECzxhJL8cbpwseilj8pLYC1ieYaeUpP7tUMLF5evSIjg7u0yB8WC2GcknBOXYLzqzlg==

# ASR Configuration
HF_API_TOKEN=*************************************
WHISPER_MODEL=small
ASR_BACKEND=hf

# Application Configuration
LOG_LEVEL=info
NODE_ENV=development

# Optional Features
ENABLE_ANALYTICS=false
ENABLE_MONITORING=false

# Development Tools
ANALYZE=false

# CI/CD
CI=false

# Database (Auto-configured by Supabase)
POSTGRES_URL=postgres://postgres.yxxabpixkjmzmipxsxpq:<EMAIL>:6543/postgres?sslmode=require&supa=base-pooler.x
POSTGRES_PRISMA_URL=postgres://postgres.yxxabpixkjmzmipxsxpq:<EMAIL>:6543/postgres?sslmode=require&supa=base-pooler.x
POSTGRES_URL_NON_POOLING=postgres://postgres.yxxabpixkjmzmipxsxpq:<EMAIL>:5432/postgres?sslmode=require
POSTGRES_USER=postgres
POSTGRES_PASSWORD=yVwWl2X9K2p4pRqC
POSTGRES_DATABASE=postgres
POSTGRES_HOST=db.yxxabpixkjmzmipxsxpq.supabase.co
