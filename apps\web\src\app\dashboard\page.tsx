import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import Link from "next/link"
import { Card, CardHeader, CardTitle, CardContent, Button } from "@reality-scripts/ui"

export default async function Dashboard() {
  const supabase = createServerComponentClient({ cookies })

  const { data: videos, error } = await supabase.from("videos").select("*").order("created_at", { ascending: false })

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Your Videos</h1>
        <Link href="/dashboard/upload">
          <Button>Upload New</Button>
        </Link>
      </div>

      {error && (
        <div className="p-4 mb-4 bg-red-100 border border-red-400 text-red-700 rounded">
          Error loading videos: {error.message}
        </div>
      )}

      {videos && videos.length === 0 && (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="mb-4">You haven&apos;t uploaded any videos yet.</p>
            <Link href="/dashboard/upload">
              <Button>Upload Your First Video</Button>
            </Link>
          </CardContent>
        </Card>
      )}

      {videos && videos.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {videos.map((video) => (
            <Link key={video.id} href={`/dashboard/videos/${video.id}`}>
              <Card className="h-full cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="truncate">{video.storage_path.split("/").pop()}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Status:</span>
                      <span className="font-medium">{video.status}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Language:</span>
                      <span className="font-medium">{video.locale}</span>
                    </div>
                    {video.expires_at && (
                      <div className="flex justify-between text-sm">
                        <span>Expires:</span>
                        <span className="font-medium">{new Date(video.expires_at).toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </div>
  )
}
