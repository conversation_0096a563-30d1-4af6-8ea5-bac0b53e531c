import { createClient } from "@supabase/supabase-js"
import type { Database } from "./types"
import { createLogger, trackError } from "../monitoring/logger"
import { metrics, measureTime } from "../monitoring/metrics"
import { healthMonitor } from "../monitoring/health"

const logger = createLogger("supabase")

// Enhanced client configuration with better error handling and security
export const createSupabaseClient = (supabaseUrl: string, supabaseKey: string) => {
  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Supabase URL and key are required")
  }

  const client = createClient<Database>(supabaseUrl, supabaseKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      flowType: "pkce", // Enhanced security
    },
    db: {
      schema: "public",
    },
    global: {
      headers: {
        "x-application-name": "reality-scripts",
      },
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  })

  // Enhanced health check with connection pooling validation
  healthMonitor.registerCheck("supabase", async () => {
    try {
      const startTime = Date.now()
      const { error } = await client.from("videos").select("id").limit(1)
      const responseTime = Date.now() - startTime

      if (error) throw error

      return {
        name: "supabase",
        status: "healthy" as const,
        message: `Database connection successful (${responseTime}ms)`,
        timestamp: new Date(),
        metadata: { responseTime },
      }
    } catch (error) {
      trackError(error as Error, { component: "supabase-health-check" })
      return {
        name: "supabase",
        status: "unhealthy" as const,
        message: error instanceof Error ? error.message : "Database connection failed",
        timestamp: new Date(),
      }
    }
  })

  return client
}

// Enhanced server client with better cookie handling
export const createSupabaseServerClient = (
  supabaseUrl: string,
  supabaseKey: string,
  cookies: () => {
    get: (name: string) => string | undefined
    set: (name: string, value: string, options: any) => void
    remove: (name: string) => void
  },
) => {
  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Supabase URL and key are required")
  }

  const supabase = createClient<Database>(supabaseUrl, supabaseKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: false,
      flowType: "pkce",
      storageKey: "sb-auth-token",
      storage: {
        getItem: (key) => {
          try {
            return cookies().get(key) ?? null
          } catch (error) {
            logger.error({ error, key }, "Failed to get cookie")
            return null
          }
        },
        setItem: (key, value) => {
          try {
            cookies().set(key, value, {
              path: "/",
              maxAge: 60 * 60 * 24 * 365,
              sameSite: "lax",
              secure: process.env.NODE_ENV === "production",
              httpOnly: false, // Required for client-side access
            })
          } catch (error) {
            logger.error({ error, key }, "Failed to set cookie")
          }
        },
        removeItem: (key) => {
          try {
            cookies().remove(key)
          } catch (error) {
            logger.error({ error, key }, "Failed to remove cookie")
          }
        },
      },
    },
    db: {
      schema: "public",
    },
    global: {
      headers: {
        "x-application-name": "reality-scripts",
      },
    },
  })

  return supabase
}

// Enhanced database service with comprehensive error handling and validation
export class DatabaseService {
  constructor(private client: ReturnType<typeof createSupabaseClient>) {}

  // Input validation helper
  private validateUUID(id: string, fieldName: string): void {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      throw new Error(`Invalid ${fieldName}: must be a valid UUID`)
    }
  }

  // Enhanced video operations with validation
  async getVideos(userId: string) {
    this.validateUUID(userId, "userId")

    return measureTime(
      "database.getVideos",
      async () => {
        logger.info({ userId }, "Fetching videos for user")

        const { data, error } = await this.client
          .from("videos")
          .select(`
            id,
            storage_path,
            locale,
            expires_at,
            status,
            created_at,
            transcriptions!inner(
              id,
              language,
              created_at
            )
          `)
          .eq("user_id", userId)
          .order("created_at", { ascending: false })

        if (error) {
          trackError(new Error(`Failed to fetch videos: ${error.message}`), {
            userId,
            errorCode: error.code,
            errorDetails: error.details,
          })
          metrics.increment("database.videos.fetch.error")
          throw new Error(`Database error: ${error.message}`)
        }

        metrics.increment("database.videos.fetch.success")
        metrics.gauge("database.videos.count", data?.length || 0)
        logger.info({ userId, count: data?.length || 0 }, "Videos fetched successfully")
        return data
      },
      { operation: "getVideos", userId },
    )
  }

  async getVideo(videoId: string, userId: string) {
    this.validateUUID(videoId, "videoId")
    this.validateUUID(userId, "userId")

    return measureTime(
      "database.getVideo",
      async () => {
        logger.info({ videoId, userId }, "Fetching video")

        const { data, error } = await this.client
          .from("videos")
          .select(`
            *,
            transcriptions(
              id,
              raw,
              text_clean,
              words,
              language,
              created_at
            ),
            exports(
              id,
              type,
              storage_path,
              created_at
            )
          `)
          .eq("id", videoId)
          .eq("user_id", userId)
          .single()

        if (error) {
          if (error.code === "PGRST116") {
            throw new Error("Video not found")
          }
          trackError(new Error(`Failed to fetch video: ${error.message}`), {
            videoId,
            userId,
            errorCode: error.code,
          })
          metrics.increment("database.video.fetch.error")
          throw new Error(`Database error: ${error.message}`)
        }

        metrics.increment("database.video.fetch.success")
        logger.info({ videoId, userId }, "Video fetched successfully")
        return data
      },
      { operation: "getVideo", videoId, userId },
    )
  }

  async createVideo(videoData: Database["public"]["Tables"]["videos"]["Insert"]) {
    // Validate required fields
    if (!videoData.user_id || !videoData.storage_path) {
      throw new Error("user_id and storage_path are required")
    }

    this.validateUUID(videoData.user_id, "user_id")

    // Validate locale
    const supportedLocales = ["en", "es", "fr", "de", "ar", "zh", "ja"]
    if (videoData.locale && !supportedLocales.includes(videoData.locale)) {
      throw new Error(`Unsupported locale: ${videoData.locale}`)
    }

    return measureTime(
      "database.createVideo",
      async () => {
        logger.info({ userId: videoData.user_id }, "Creating video record")

        const { data, error } = await this.client
          .from("videos")
          .insert({
            ...videoData,
            created_at: new Date().toISOString(),
          })
          .select()
          .single()

        if (error) {
          trackError(new Error(`Failed to create video: ${error.message}`), {
            videoData: { ...videoData, user_id: "[REDACTED]" },
            errorCode: error.code,
          })
          metrics.increment("database.video.create.error")
          throw new Error(`Database error: ${error.message}`)
        }

        metrics.increment("database.video.create.success")
        logger.info({ videoId: data.id, userId: videoData.user_id }, "Video created successfully")
        return data
      },
      { operation: "createVideo", userId: videoData.user_id },
    )
  }

  async updateVideo(videoId: string, updates: Database["public"]["Tables"]["videos"]["Update"]) {
    this.validateUUID(videoId, "videoId")

    return measureTime(
      "database.updateVideo",
      async () => {
        logger.info({ videoId, updates: Object.keys(updates) }, "Updating video")

        const { data, error } = await this.client
          .from("videos")
          .update({
            ...updates,
            updated_at: new Date().toISOString(),
          })
          .eq("id", videoId)
          .select()
          .single()

        if (error) {
          trackError(new Error(`Failed to update video: ${error.message}`), {
            videoId,
            updates: Object.keys(updates),
            errorCode: error.code,
          })
          metrics.increment("database.video.update.error")
          throw new Error(`Database error: ${error.message}`)
        }

        metrics.increment("database.video.update.success")
        logger.info({ videoId }, "Video updated successfully")
        return data
      },
      { operation: "updateVideo", videoId },
    )
  }

  async getTranscription(videoId: string) {
    this.validateUUID(videoId, "videoId")

    return measureTime(
      "database.getTranscription",
      async () => {
        logger.info({ videoId }, "Fetching transcription")

        const { data, error } = await this.client.from("transcriptions").select("*").eq("video_id", videoId).single()

        if (error && error.code !== "PGRST116") {
          trackError(new Error(`Failed to fetch transcription: ${error.message}`), {
            videoId,
            errorCode: error.code,
          })
          metrics.increment("database.transcription.fetch.error")
          throw new Error(`Database error: ${error.message}`)
        }

        metrics.increment("database.transcription.fetch.success")
        logger.info({ videoId, found: !!data }, "Transcription fetch completed")
        return data
      },
      { operation: "getTranscription", videoId },
    )
  }

  // Enhanced storage operations with security validation
  async createSignedUrl(bucket: string, path: string, expiresIn = 3600) {
    // Validate bucket name
    const allowedBuckets = ["raw", "audio", "proxy", "exports"]
    if (!allowedBuckets.includes(bucket)) {
      throw new Error(`Invalid bucket: ${bucket}`)
    }

    // Validate expiration time (max 24 hours)
    if (expiresIn > 86400) {
      throw new Error("Expiration time cannot exceed 24 hours")
    }

    return measureTime(
      "storage.createSignedUrl",
      async () => {
        logger.info({ bucket, path, expiresIn }, "Creating signed URL")

        const { data, error } = await this.client.storage.from(bucket).createSignedUrl(path, expiresIn)

        if (error) {
          trackError(new Error(`Failed to create signed URL: ${error.message}`), {
            bucket,
            path,
            errorCode: error.name,
          })
          metrics.increment("storage.signedUrl.error")
          throw new Error(`Storage error: ${error.message}`)
        }

        metrics.increment("storage.signedUrl.success")
        logger.info({ bucket, path }, "Signed URL created successfully")
        return data
      },
      { operation: "createSignedUrl", bucket },
    )
  }
}
