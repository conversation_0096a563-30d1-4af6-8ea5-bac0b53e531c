// Create WebVTT file from word timestamps
export function createVttFile(words: Array<{ text: string; start: number; end: number }>): string {
  const vttLines = ["WEBVTT", ""]

  // Group words into cues (max 10 words per cue)
  const cueSize = 10
  for (let i = 0; i < words.length; i += cueSize) {
    const cueWords = words.slice(i, i + cueSize)
    const startTime = formatVttTime(cueWords[0].start)
    const endTime = formatVttTime(cueWords[cueWords.length - 1].end)
    const text = cueWords.map((w) => w.text).join(" ")

    vttLines.push(`${startTime} --> ${endTime}`)
    vttLines.push(text)
    vttLines.push("")
  }

  return vttLines.join("\n")
}

// Format time for WebVTT (HH:MM:SS.mmm)
function formatVttTime(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  const msStr = String(ms % 1000).padStart(3, "0")
  const secStr = String(seconds % 60).padStart(2, "0")
  const minStr = String(minutes % 60).padStart(2, "0")
  const hourStr = String(hours).padStart(2, "0")

  return `${hourStr}:${minStr}:${secStr}.${msStr}`
}
