# RealityScripts - Video Transcription & Intelligence Platform

[![Production Deployment](https://github.com/your-org/reality-scripts/workflows/Production%20Deployment/badge.svg)](https://github.com/your-org/reality-scripts/actions)
[![Test Coverage](https://codecov.io/gh/your-org/reality-scripts/branch/main/graph/badge.svg)](https://codecov.io/gh/your-org/reality-scripts)
[![Security Rating](https://img.shields.io/badge/security-A+-green.svg)](https://github.com/your-org/reality-scripts)

## 🚀 Production Ready

RealityScripts is a scalable, full-stack platform that transforms video and audio content into rich, searchable, and interactive transcripts. Built with Next.js 15, Supabase, and modern TypeScript.

### ✨ Key Features

- **🎯 Audio-First Transcription** - Whisper ASR with word-level timestamps
- **🔄 Click-to-Seek Playback** - Interactive transcript navigation
- **⏰ Smart Retention** - Configurable TTL with user-controlled extensions
- **🌍 Multilingual Support** - Full i18n with RTL support for Arabic
- **🔒 Enterprise Security** - Row-level security, PKCE auth, comprehensive validation
- **📊 Real-time Analytics** - Performance monitoring and health checks
- **🎨 Modern UI** - Responsive design with dark mode support

### 🏗️ Architecture

\`\`\`
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │────│   Supabase DB   │────│  Worker Service │
│   (Frontend)    │    │   (Backend)     │    │   (Processing)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Supabase Storage│
                    │   (File Store)  │
                    └─────────────────┘
\`\`\`

### 🛠️ Tech Stack

- **Frontend**: Next.js 15, TypeScript, Tailwind CSS, Radix UI
- **Backend**: Supabase (PostgreSQL, Auth, Storage, RLS)
- **Processing**: Node.js Worker, FFmpeg, Whisper ASR
- **Testing**: Jest, React Testing Library, Playwright
- **Deployment**: Vercel, Docker, GitHub Actions

### 🚦 Getting Started

1. **Clone the repository**
   \`\`\`bash
   git clone https://github.com/your-org/reality-scripts.git
   cd reality-scripts
   \`\`\`

2. **Install dependencies**
   \`\`\`bash
   npm install
   \`\`\`

3. **Set up environment variables**
   \`\`\`bash
   cp .env.example .env.local
   # Edit .env.local with your Supabase credentials
   \`\`\`

4. **Run database migrations**
   \`\`\`bash
   npm run db:migrate
   \`\`\`

5. **Start development server**
   \`\`\`bash
   npm run dev
   \`\`\`

### 🧪 Testing

\`\`\`bash
# Run all tests
npm run test:all

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:e2e

# Run with coverage
npm run test:coverage
\`\`\`

### 🚀 Deployment

The application is automatically deployed to Vercel on every push to `main`:

- **Production**: https://realityscripts.vercel.app
- **Health Check**: https://realityscripts.vercel.app/api/health
- **Metrics**: https://realityscripts.vercel.app/api/metrics

### 📊 Monitoring

- **Health Checks**: Automated endpoint monitoring
- **Performance Metrics**: Response time and throughput tracking
- **Error Tracking**: Comprehensive error logging and alerting
- **Security Monitoring**: Authentication and authorization auditing

### 🔒 Security

- **Authentication**: Supabase Auth with PKCE flow
- **Authorization**: Row-level security (RLS) policies
- **Data Validation**: Comprehensive input validation
- **File Security**: Type and size validation for uploads
- **Headers**: Security headers for XSS/CSRF protection

### 📈 Performance

- **Database**: Optimized indexes and query patterns
- **Caching**: Strategic caching for static assets
- **CDN**: Global content delivery via Vercel Edge
- **Monitoring**: Real-time performance tracking

### 🌍 Internationalization

Supported languages:
- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Arabic (ar) - with RTL support
- Chinese (zh)
- Japanese (ja)

### 📝 API Documentation

- **Upload Video**: `POST /api/videos/upload`
- **Get Videos**: `GET /api/videos`
- **Get Video**: `GET /api/videos/[id]`
- **Extend TTL**: `POST /api/videos/[id]/extend-ttl`
- **Export**: `GET /api/videos/[id]/export/[format]`

### 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### 🆘 Support

- **Documentation**: [docs.realityscripts.com](https://docs.realityscripts.com)
- **Issues**: [GitHub Issues](https://github.com/your-org/reality-scripts/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/reality-scripts/discussions)

---

**Built with ❤️ by the RealityScripts Team**
