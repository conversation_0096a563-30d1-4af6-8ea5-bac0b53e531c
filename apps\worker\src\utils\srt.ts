// Create SRT file from word timestamps
export function createSrtFile(words: Array<{ text: string; start: number; end: number }>): string {
  const srtLines: string[] = []

  // Group words into cues (max 10 words per cue)
  const cueSize = 10
  for (let i = 0, cueIndex = 1; i < words.length; i += cueSize, cueIndex++) {
    const cueWords = words.slice(i, i + cueSize)
    const startTime = formatSrtTime(cueWords[0].start)
    const endTime = formatSrtTime(cueWords[cueWords.length - 1].end)
    const text = cueWords.map((w) => w.text).join(" ")

    srtLines.push(String(cueIndex))
    srtLines.push(`${startTime} --> ${endTime}`)
    srtLines.push(text)
    srtLines.push("")
  }

  return srtLines.join("\n")
}

// Format time for SRT (HH:MM:SS,mmm)
function formatSrtTime(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  const msStr = String(ms % 1000).padStart(3, "0")
  const secStr = String(seconds % 60).padStart(2, "0")
  const minStr = String(minutes % 60).padStart(2, "0")
  const hourStr = String(hours).padStart(2, "0")

  return `${hourStr}:${minStr}:${secStr},${msStr}`
}
