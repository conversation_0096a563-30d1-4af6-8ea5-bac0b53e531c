/**
 * UI Integration Tests
 *
 * Tests for component integration, data flow, and user interactions
 * across the entire application interface.
 */

import type React from "react"
import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { NextIntlClientProvider } from "next-intl"
import { ToastProvider, VideoUploader, TranscriptEditor } from "@reality-scripts/ui"

// Mock Next.js router
const mockPush = jest.fn()
const mockRefresh = jest.fn()

jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: mockPush,
    refresh: mockRefresh,
  }),
  usePathname: () => "/dashboard",
  useSearchParams: () => new URLSearchParams(),
}))

const mockMessages = {
  common: {
    appName: "RealityScripts",
    loading: "Loading...",
    error: "Error",
    success: "Success",
  },
  upload: {
    title: "Upload Video or Audio",
    dragDrop: "Drag and drop your file here",
    selectFile: "Select File",
    uploading: "Uploading...",
    uploadSuccess: "Upload Successful",
    uploadError: "Upload Failed",
  },
  video: {
    transcript: "Transcript",
    clickToSeek: "Click to seek",
  },
  errors: {
    uploadFailed: "Upload failed. Please try again.",
  },
}

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <NextIntlClientProvider locale="en" messages={mockMessages}>
    <ToastProvider>{children}</ToastProvider>
  </NextIntlClientProvider>
)

describe("UI Integration Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe("Video Upload Flow", () => {
    it("should handle complete upload workflow", async () => {
      // Arrange
      const user = userEvent.setup()
      const mockOnUpload = jest.fn().mockResolvedValue(undefined)
      const supportedLocales = [
        { value: "en", label: "English" },
        { value: "es", label: "Español" },
      ]

      render(
        <TestWrapper>
          <VideoUploader onUpload={mockOnUpload} supportedLocales={supportedLocales} />
        </TestWrapper>,
      )

      // Act - Select file
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = new File(["test content"], "test-video.mp4", { type: "video/mp4" })

      if (fileInput) {
        await user.upload(fileInput, file)
      }

      // Assert - File should be selected
      await waitFor(() => {
        expect(screen.getByText("test-video.mp4")).toBeInTheDocument()
      })

      // Act - Upload file
      const uploadButton = screen.getByRole("button", { name: /upload file/i })
      await user.click(uploadButton)

      // Assert - Upload function should be called
      expect(mockOnUpload).toHaveBeenCalledWith(file, "auto")
    })

    it("should validate file types during upload", async () => {
      // Arrange
      const user = userEvent.setup()
      const mockOnUpload = jest.fn()
      const supportedLocales = [{ value: "en", label: "English" }]

      render(
        <TestWrapper>
          <VideoUploader onUpload={mockOnUpload} supportedLocales={supportedLocales} />
        </TestWrapper>,
      )

      // Act - Try to upload invalid file type
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const invalidFile = new File(["test content"], "test.txt", { type: "text/plain" })

      if (fileInput) {
        await user.upload(fileInput, invalidFile)
      }

      // Assert - Should show error message
      await waitFor(() => {
        expect(screen.getByText(/file type.*not supported/i)).toBeInTheDocument()
      })
    })

    it("should handle upload errors gracefully", async () => {
      // Arrange
      const user = userEvent.setup()
      const mockOnUpload = jest.fn().mockRejectedValue(new Error("Upload failed"))
      const supportedLocales = [{ value: "en", label: "English" }]

      render(
        <TestWrapper>
          <VideoUploader onUpload={mockOnUpload} supportedLocales={supportedLocales} />
        </TestWrapper>,
      )

      // Act - Upload file that will fail
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = new File(["test content"], "test-video.mp4", { type: "video/mp4" })

      if (fileInput) {
        await user.upload(fileInput, file)
      }

      const uploadButton = screen.getByRole("button", { name: /upload file/i })
      await user.click(uploadButton)

      // Assert - Should show error toast
      await waitFor(() => {
        expect(screen.getByText(/upload failed/i)).toBeInTheDocument()
      })
    })

    it("should support drag and drop functionality", async () => {
      // Arrange
      const mockOnUpload = jest.fn()
      const supportedLocales = [{ value: "en", label: "English" }]

      render(
        <TestWrapper>
          <VideoUploader onUpload={mockOnUpload} supportedLocales={supportedLocales} />
        </TestWrapper>,
      )

      // Act - Simulate drag and drop
      const dropZone = screen.getByText(/drag and drop/i).closest("div")
      const file = new File(["test content"], "test-video.mp4", { type: "video/mp4" })

      if (dropZone) {
        fireEvent.dragEnter(dropZone)
        fireEvent.dragOver(dropZone, {
          dataTransfer: {
            files: [file],
          },
        })
        fireEvent.drop(dropZone, {
          dataTransfer: {
            files: [file],
          },
        })
      }

      // Assert - File should be selected
      await waitFor(() => {
        expect(screen.getByText("test-video.mp4")).toBeInTheDocument()
      })
    })
  })

  describe("Transcript Interaction", () => {
    it("should handle word click for video seeking", async () => {
      // Arrange
      const user = userEvent.setup()
      const mockOnWordClick = jest.fn()
      const words = [
        { text: "Hello", start: 0, end: 500 },
        { text: "world", start: 500, end: 1000 },
      ]

      render(
        <TestWrapper>
          <TranscriptEditor words={words} onWordClick={mockOnWordClick} />
        </TestWrapper>,
      )

      // Act - Click on a word
      const wordElement = screen.getByText("Hello")
      await user.click(wordElement)

      // Assert - Should call onWordClick with correct timestamp
      expect(mockOnWordClick).toHaveBeenCalledWith(0)
    })

    it("should highlight active word based on current time", () => {
      // Arrange
      const words = [
        { text: "Hello", start: 0, end: 500 },
        { text: "world", start: 500, end: 1000 },
      ]

      render(
        <TestWrapper>
          <TranscriptEditor words={words} currentTime={250} />
        </TestWrapper>,
      )

      // Assert - First word should be highlighted
      const helloWord = screen.getByText("Hello")
      expect(helloWord).toHaveClass("bg-primary")
    })

    it("should handle empty transcript gracefully", () => {
      // Act & Assert - Should not crash
      expect(() => {
        render(
          <TestWrapper>
            <TranscriptEditor words={[]} />
          </TestWrapper>,
        )
      }).not.toThrow()
    })
  })

  describe("Responsive Design", () => {
    it("should adapt to mobile viewport", () => {
      // Arrange - Mock mobile viewport
      Object.defineProperty(window, "innerWidth", {
        writable: true,
        configurable: true,
        value: 375,
      })

      // Act
      render(
        <TestWrapper>
          <VideoUploader onUpload={jest.fn()} supportedLocales={[{ value: "en", label: "English" }]} />
        </TestWrapper>,
      )

      // Assert - Should render mobile-friendly layout
      const container = screen.getByText(/drag and drop/i).closest("div")
      expect(container).toHaveClass("max-w-2xl")
    })

    it("should handle tablet viewport correctly", () => {
      // Arrange - Mock tablet viewport
      Object.defineProperty(window, "innerWidth", {
        writable: true,
        configurable: true,
        value: 768,
      })

      // Act
      render(
        <TestWrapper>
          <VideoUploader onUpload={jest.fn()} supportedLocales={[{ value: "en", label: "English" }]} />
        </TestWrapper>,
      )

      // Assert - Should render tablet-friendly layout
      expect(screen.getByText(/drag and drop/i)).toBeInTheDocument()
    })
  })

  describe("Toast Notifications", () => {
    it("should display success toast for successful operations", async () => {
      // Arrange
      const user = userEvent.setup()
      const mockOnUpload = jest.fn().mockResolvedValue(undefined)

      render(
        <TestWrapper>
          <VideoUploader onUpload={mockOnUpload} supportedLocales={[{ value: "en", label: "English" }]} />
        </TestWrapper>,
      )

      // Act - Complete successful upload
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = new File(["test content"], "test-video.mp4", { type: "video/mp4" })

      if (fileInput) {
        await user.upload(fileInput, file)
      }

      const uploadButton = screen.getByRole("button", { name: /upload file/i })
      await user.click(uploadButton)

      // Assert - Should show success toast
      await waitFor(() => {
        expect(screen.getByText(/upload successful/i)).toBeInTheDocument()
      })
    })

    it("should auto-dismiss toasts after timeout", async () => {
      // This test would require mocking timers
      jest.useFakeTimers()

      // Arrange
      const user = userEvent.setup()
      const mockOnUpload = jest.fn().mockResolvedValue(undefined)

      render(
        <TestWrapper>
          <VideoUploader onUpload={mockOnUpload} supportedLocales={[{ value: "en", label: "English" }]} />
        </TestWrapper>,
      )

      // Act - Trigger toast
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = new File(["test content"], "test-video.mp4", { type: "video/mp4" })

      if (fileInput) {
        await user.upload(fileInput, file)
      }

      const uploadButton = screen.getByRole("button", { name: /upload file/i })
      await user.click(uploadButton)

      // Wait for toast to appear
      await waitFor(() => {
        expect(screen.getByText(/upload successful/i)).toBeInTheDocument()
      })

      // Fast-forward time
      jest.advanceTimersByTime(5000)

      // Assert - Toast should be dismissed
      await waitFor(() => {
        expect(screen.queryByText(/upload successful/i)).not.toBeInTheDocument()
      })

      jest.useRealTimers()
    })
  })

  describe("Error Boundaries", () => {
    it("should catch and display component errors", () => {
      // Arrange - Component that throws error
      const ThrowError = () => {
        throw new Error("Test error")
      }

      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {})

      // Act & Assert
      expect(() => {
        render(
          <TestWrapper>
            <ThrowError />
          </TestWrapper>,
        )
      }).not.toThrow() // Error boundary should catch it

      consoleSpy.mockRestore()
    })
  })

  describe("Accessibility Integration", () => {
    it("should maintain focus management across components", async () => {
      // Arrange
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <VideoUploader onUpload={jest.fn()} supportedLocales={[{ value: "en", label: "English" }]} />
        </TestWrapper>,
      )

      // Act - Navigate with keyboard
      await user.tab()

      // Assert - Should focus on interactive element
      expect(document.activeElement).toBeInTheDocument()
    })

    it("should provide proper screen reader support", () => {
      // Arrange
      render(
        <TestWrapper>
          <VideoUploader onUpload={jest.fn()} supportedLocales={[{ value: "en", label: "English" }]} />
        </TestWrapper>,
      )

      // Assert - Should have proper ARIA labels
      expect(screen.getByRole("button")).toHaveAccessibleName()
    })
  })
})
