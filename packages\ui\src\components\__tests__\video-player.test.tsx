import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { VideoPlayer } from "../video-player"

describe("VideoPlayer Component", () => {
  const defaultProps = {
    src: "https://example.com/video.mp4",
    poster: "https://example.com/poster.jpg",
    vttSrc: "https://example.com/subtitles.vtt",
  }

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks()
  })

  describe("Rendering", () => {
    it("should render video element with correct attributes", () => {
      // Act
      render(<VideoPlayer {...defaultProps} />)

      // Assert
      const video = screen.getByRole("application") || document.querySelector("video")
      expect(video).toBeInTheDocument()
      expect(video).toHaveAttribute("poster", defaultProps.poster)
      expect(video).toHaveAttribute("preload", "metadata")
    })

    it("should render video source with correct src", () => {
      // Act
      render(<VideoPlayer {...defaultProps} />)

      // Assert
      const source = document.querySelector("source")
      expect(source).toHaveAttribute("src", defaultProps.src)
    })

    it("should render subtitle track when vttSrc is provided", () => {
      // Act
      render(<VideoPlayer {...defaultProps} />)

      // Assert
      const track = document.querySelector("track")
      expect(track).toHaveAttribute("src", defaultProps.vttSrc)
      expect(track).toHaveAttribute("kind", "subtitles")
      expect(track).toHaveAttribute("default")
    })

    it("should not render subtitle track when vttSrc is not provided", () => {
      // Act
      render(<VideoPlayer src={defaultProps.src} />)

      // Assert
      const track = document.querySelector("track")
      expect(track).not.toBeInTheDocument()
    })
  })

  describe("Playback Controls", () => {
    it("should render play button initially", () => {
      // Act
      render(<VideoPlayer {...defaultProps} />)

      // Assert
      const playButton = screen.getByRole("button", { name: /play/i })
      expect(playButton).toBeInTheDocument()
    })

    it("should toggle play/pause when button is clicked", async () => {
      // Arrange
      const user = userEvent.setup()
      render(<VideoPlayer {...defaultProps} />)

      // Act
      const playButton = screen.getByRole("button", { name: /play/i })
      await user.click(playButton)

      // Assert
      expect(HTMLMediaElement.prototype.play).toHaveBeenCalled()
    })

    it("should update button label when playing state changes", async () => {
      // Arrange
      const user = userEvent.setup()
      render(<VideoPlayer {...defaultProps} />)

      // Act
      const playButton = screen.getByRole("button", { name: /play/i })
      await user.click(playButton)

      // Assert
      await waitFor(() => {
        expect(screen.getByRole("button", { name: /pause/i })).toBeInTheDocument()
      })
    })

    it("should handle volume control", async () => {
      // Arrange
      const user = userEvent.setup()
      render(<VideoPlayer {...defaultProps} />)

      // Act
      const volumeSlider = screen.getByDisplayValue("1") // Default volume
      await user.clear(volumeSlider)
      await user.type(volumeSlider, "0.5")

      // Assert
      expect(volumeSlider).toHaveValue(0.5)
    })

    it("should handle progress control", async () => {
      // Arrange
      const user = userEvent.setup()
      render(<VideoPlayer {...defaultProps} />)

      // Mock video duration
      const video = document.querySelector("video")
      if (video) {
        Object.defineProperty(video, "duration", { value: 100, writable: true })
        fireEvent.loadedMetadata(video)
      }

      // Act
      const progressSlider = screen.getByDisplayValue("0") // Default progress
      await user.clear(progressSlider)
      await user.type(progressSlider, "50")

      // Assert
      expect(progressSlider).toHaveValue(50)
    })
  })

  describe("Time Display", () => {
    it("should format time correctly", () => {
      // Act
      render(<VideoPlayer {...defaultProps} />)

      // Assert
      expect(screen.getByText("0:00 / 0:00")).toBeInTheDocument()
    })

    it("should update time display during playback", () => {
      // Arrange
      render(<VideoPlayer {...defaultProps} />)
      const video = document.querySelector("video")

      // Act
      if (video) {
        Object.defineProperty(video, "currentTime", { value: 65, writable: true })
        Object.defineProperty(video, "duration", { value: 120, writable: true })
        fireEvent.timeUpdate(video)
      }

      // Assert
      expect(screen.getByText("1:05 / 2:00")).toBeInTheDocument()
    })
  })

  describe("Accessibility", () => {
    it("should provide proper ARIA labels for controls", () => {
      // Act
      render(<VideoPlayer {...defaultProps} />)

      // Assert
      expect(screen.getByRole("button", { name: /play/i })).toBeInTheDocument()
      expect(screen.getByRole("slider")).toBeInTheDocument() // Volume slider
    })

    it("should support keyboard navigation", async () => {
      // Arrange
      const user = userEvent.setup()
      render(<VideoPlayer {...defaultProps} />)

      // Act
      const playButton = screen.getByRole("button", { name: /play/i })
      playButton.focus()
      await user.keyboard("{Enter}")

      // Assert
      expect(HTMLMediaElement.prototype.play).toHaveBeenCalled()
    })

    it("should handle focus management correctly", async () => {
      // Arrange
      const user = userEvent.setup()
      render(<VideoPlayer {...defaultProps} />)

      // Act
      await user.tab() // Should focus on play button

      // Assert
      expect(screen.getByRole("button", { name: /play/i })).toHaveFocus()
    })
  })

  describe("Event Handling", () => {
    it("should call onTimeUpdate when provided", () => {
      // Arrange
      const onTimeUpdate = jest.fn()
      render(<VideoPlayer {...defaultProps} onTimeUpdate={onTimeUpdate} />)
      const video = document.querySelector("video")

      // Act
      if (video) {
        Object.defineProperty(video, "currentTime", { value: 10, writable: true })
        fireEvent.timeUpdate(video)
      }

      // Assert
      expect(onTimeUpdate).toHaveBeenCalledWith(10)
    })

    it("should seek to specific time when currentTime prop changes", () => {
      // Arrange
      const { rerender } = render(<VideoPlayer {...defaultProps} currentTime={0} />)
      const video = document.querySelector("video")

      // Act
      rerender(<VideoPlayer {...defaultProps} currentTime={30} />)

      // Assert
      if (video) {
        expect(video.currentTime).toBe(30)
      }
    })

    it("should handle invalid currentTime gracefully", () => {
      // Arrange
      const { rerender } = render(<VideoPlayer {...defaultProps} currentTime={0} />)

      // Act & Assert - Should not throw
      expect(() => {
        rerender(<VideoPlayer {...defaultProps} currentTime={Number.NaN} />)
      }).not.toThrow()
    })
  })

  describe("Responsive Behavior", () => {
    it("should apply responsive classes", () => {
      // Act
      render(<VideoPlayer {...defaultProps} className="custom-class" />)

      // Assert
      const container = document.querySelector(".custom-class")
      expect(container).toBeInTheDocument()
    })

    it("should maintain aspect ratio on different screen sizes", () => {
      // Act
      render(<VideoPlayer {...defaultProps} />)
      const video = document.querySelector("video")

      // Assert
      expect(video).toHaveClass("w-full", "rounded-lg")
    })
  })

  describe("Error Handling", () => {
    it("should handle video load errors gracefully", () => {
      // Arrange
      render(<VideoPlayer {...defaultProps} />)
      const video = document.querySelector("video")

      // Act
      if (video) {
        fireEvent.error(video)
      }

      // Assert - Should not crash
      expect(video).toBeInTheDocument()
    })

    it("should handle missing video source", () => {
      // Act & Assert - Should not throw
      expect(() => {
        render(<VideoPlayer src="" />)
      }).not.toThrow()
    })
  })
})
