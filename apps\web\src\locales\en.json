{"common": {"appName": "RealityScripts", "tagline": "Transform your videos into rich, searchable, and interactive transcripts", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "dashboard": "Dashboard", "upload": "Upload", "videos": "Videos", "settings": "Settings"}, "dashboard": {"yourVideos": "Your Videos", "uploadNew": "Upload New", "noVideos": "You haven't uploaded any videos yet.", "uploadFirst": "Upload Your First Video", "status": "Status", "language": "Language", "expires": "Expires"}, "upload": {"title": "Upload Video or Audio", "dragDrop": "Drag and drop your file here", "selectFile": "Select File", "supportedFormats": "Supported formats: .mp3, .ogg, .mp4, .mov", "maxFileSize": "Maximum file size: 1GB", "autoDelete": "Videos will be automatically deleted after 72 hours unless extended", "transcriptsPreserved": "Transcripts and low-resolution proxies will be preserved", "uploading": "Uploading...", "language": "Language", "autoDetect": "Auto-detect"}, "video": {"transcript": "Transcript", "transcriptionInProgress": "Transcription in progress...", "noTranscription": "No transcription available", "processingVideo": "Processing video...", "videoNotAvailable": "Video not available", "extendRetention": "Extend Retention (72 hours)", "downloadSRT": "Download SRT", "downloadVTT": "Download VTT", "downloadText": "Download Text", "expiresIn": "Video expires in {hours} {hours, plural, one {hour} other {hours}}", "expiryWarning": "The original video will be deleted, but the transcript will remain available.", "extend": "Extend 72 Hours"}, "errors": {"uploadFailed": "Upload failed. Please try again.", "unauthorized": "Unauthorized. Please login.", "videoNotFound": "Video not found.", "transcriptionNotFound": "Transcription not found.", "exportNotFound": "Export not found.", "failedToExtend": "Failed to extend video retention. Please try again."}}