export type SupportedLocale = "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "zh" | "ja" | "ko" | "ar" | "hi"

export interface LocaleConfig {
  code: SupportedLocale
  name: string
  nativeName: string
  direction: "ltr" | "rtl"
  flag: string
  whisperCode?: string // For Whisper ASR mapping
}

export interface TranslationKeys {
  common: {
    appName: string
    tagline: string
    login: string
    register: string
    logout: string
    dashboard: string
    upload: string
    videos: string
    settings: string
    loading: string
    error: string
    success: string
    cancel: string
    save: string
    delete: string
    edit: string
    back: string
    next: string
    previous: string
  }
  dashboard: {
    yourVideos: string
    uploadNew: string
    noVideos: string
    uploadFirst: string
    status: string
    language: string
    expires: string
    processing: string
    completed: string
    failed: string
  }
  upload: {
    title: string
    dragDrop: string
    selectFile: string
    supportedFormats: string
    maxFileSize: string
    autoDelete: string
    transcriptsPreserved: string
    uploading: string
    language: string
    autoDetect: string
    uploadSuccess: string
    uploadError: string
  }
  video: {
    transcript: string
    transcriptionInProgress: string
    noTranscription: string
    processingVideo: string
    videoNotAvailable: string
    extendRetention: string
    downloadSRT: string
    downloadVTT: string
    downloadText: string
    expiresIn: string
    expiryWarning: string
    extend: string
    clickToSeek: string
    duration: string
    fileSize: string
  }
  errors: {
    uploadFailed: string
    unauthorized: string
    videoNotFound: string
    transcriptionNotFound: string
    exportNotFound: string
    failedToExtend: string
    networkError: string
    serverError: string
    validationError: string
  }
  auth: {
    loginTitle: string
    registerTitle: string
    email: string
    password: string
    confirmPassword: string
    forgotPassword: string
    rememberMe: string
    alreadyHaveAccount: string
    dontHaveAccount: string
    passwordRequirements: string
    loginSuccess: string
    registerSuccess: string
    logoutSuccess: string
  }
}
