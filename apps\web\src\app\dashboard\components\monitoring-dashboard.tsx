"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardHeader, CardTitle, CardContent, LoadingSpinner } from "@reality-scripts/ui"
import { createLogger } from "@reality-scripts/lib"
import { Activity, AlertTriangle, CheckCircle, Clock, Database, Server } from "lucide-react"

const logger = createLogger("monitoring-dashboard")

interface HealthCheck {
  name: string
  status: "healthy" | "unhealthy" | "degraded"
  message?: string
  timestamp: string
  responseTime?: number
}

interface SystemHealth {
  status: "healthy" | "unhealthy" | "degraded"
  checks: HealthCheck[]
  timestamp: string
}

interface Metric {
  name: string
  value: number
  tags?: Record<string, string>
  timestamp: string
}

export default function MonitoringDashboard() {
  const [health, setHealth] = useState<SystemHealth | null>(null)
  const [metrics, setMetrics] = useState<Metric[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchHealthData = async () => {
      try {
        const [healthResponse, metricsResponse] = await Promise.all([fetch("/api/health"), fetch("/api/metrics")])

        if (healthResponse.ok) {
          const healthData = await healthResponse.json()
          setHealth(healthData)
        }

        if (metricsResponse.ok) {
          const metricsData = await metricsResponse.json()
          setMetrics(metricsData.metrics || [])
        }
      } catch (err) {
        logger.error({ error: err }, "Failed to fetch monitoring data")
        setError("Failed to load monitoring data")
      } finally {
        setLoading(false)
      }
    }

    fetchHealthData()

    // Refresh every 30 seconds
    const interval = setInterval(fetchHealthData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy":
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case "degraded":
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case "unhealthy":
        return <AlertTriangle className="w-5 h-5 text-red-500" />
      default:
        return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "text-green-600 bg-green-50 border-green-200"
      case "degraded":
        return "text-yellow-600 bg-yellow-50 border-yellow-200"
      case "unhealthy":
        return "text-red-600 bg-red-50 border-red-200"
      default:
        return "text-gray-600 bg-gray-50 border-gray-200"
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" text="Loading monitoring data..." />
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <p className="text-red-600">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {health && getStatusIcon(health.status)}
              <span
                className={`text-2xl font-bold ${
                  health?.status === "healthy"
                    ? "text-green-600"
                    : health?.status === "degraded"
                      ? "text-yellow-600"
                      : "text-red-600"
                }`}
              >
                {health?.status || "Unknown"}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              Last updated: {health ? new Date(health.timestamp).toLocaleTimeString() : "Never"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Health Checks</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{health?.checks.length || 0}</div>
            <p className="text-xs text-muted-foreground">
              {health?.checks.filter((c) => c.status === "healthy").length || 0} healthy
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Metrics</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.length}</div>
            <p className="text-xs text-muted-foreground">Collected metrics</p>
          </CardContent>
        </Card>
      </div>

      {/* Health Checks Detail */}
      {health && (
        <Card>
          <CardHeader>
            <CardTitle>Health Checks</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {health.checks.map((check, index) => (
                <div key={index} className={`p-4 rounded-lg border ${getStatusColor(check.status)}`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(check.status)}
                      <div>
                        <h3 className="font-medium">{check.name}</h3>
                        {check.message && <p className="text-sm opacity-75">{check.message}</p>}
                      </div>
                    </div>
                    <div className="text-right">
                      {check.responseTime && <p className="text-sm font-medium">{check.responseTime}ms</p>}
                      <p className="text-xs opacity-75">{new Date(check.timestamp).toLocaleTimeString()}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Metrics */}
      {metrics.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {metrics.slice(-10).map((metric, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
                >
                  <div>
                    <span className="font-medium">{metric.name}</span>
                    {metric.tags && (
                      <div className="text-xs text-gray-500">
                        {Object.entries(metric.tags).map(([key, value]) => (
                          <span key={key} className="mr-2">
                            {key}: {value}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <span className="font-bold">{metric.value}</span>
                    <div className="text-xs text-gray-500">{new Date(metric.timestamp).toLocaleTimeString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
