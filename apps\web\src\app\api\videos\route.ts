import { type NextRequest, NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import type { Database } from "@reality-scripts/lib"

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check if user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get("limit") || "10")
    const offset = parseInt(searchParams.get("offset") || "0")
    const status = searchParams.get("status")

    // Build query
    let query = supabase
      .from("videos")
      .select("*")
      .eq("user_id", session.user.id)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    // Filter by status if provided
    if (status) {
      query = query.eq("status", status)
    }

    const { data: videos, error } = await query

    if (error) {
      console.error("Error fetching videos:", error)
      return NextResponse.json({ error: "Failed to fetch videos" }, { status: 500 })
    }

    return NextResponse.json({ videos })
  } catch (error) {
    console.error("Videos API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check if user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { storage_path, locale = "en" } = body

    if (!storage_path) {
      return NextResponse.json({ error: "storage_path is required" }, { status: 400 })
    }

    // Create video record
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 72) // 72 hour TTL

    const { data: video, error } = await supabase
      .from("videos")
      .insert({
        user_id: session.user.id,
        storage_path,
        locale,
        expires_at: expiresAt.toISOString(),
        status: "uploaded",
      })
      .select()
      .single()

    if (error) {
      console.error("Error creating video:", error)
      return NextResponse.json({ error: "Failed to create video" }, { status: 500 })
    }

    return NextResponse.json({ video }, { status: 201 })
  } catch (error) {
    console.error("Videos POST API error:", error)
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: "Invalid JSON" }, { status: 400 })
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
