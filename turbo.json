{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "env": ["NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "ANALYZE"]}, "dev": {"cache": false, "persistent": true, "env": ["NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY", "HF_API_TOKEN", "WHISPER_MODEL", "ASR_BACKEND", "LOG_LEVEL"]}, "lint": {"dependsOn": ["^build"], "outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"]}, "test:coverage": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["build"], "outputs": ["test-results/**", "playwright-report/**"]}, "clean": {"cache": false}}, "remoteCache": {"signature": true}}