import type { SupabaseClient } from "@supabase/supabase-js"
import { logger } from "../utils/logger"
import { downloadFile, uploadFile } from "../utils/storage"
import type { AsrClient } from "../asr/AsrClient"
import { HuggingFaceWhisperClient } from "../asr/HuggingFaceWhisperClient"
import { LocalWhisperClient } from "../asr/LocalWhisperClient"
import { createVttFile } from "../utils/vtt"

// Process transcription job
export async function transcribeJob(supabase: SupabaseClient, job: any) {
  const { video_id, input } = job

  // Get video details
  const { data: video, error: videoError } = await supabase.from("videos").select("*").eq("id", video_id).single()

  if (videoError || !video) {
    throw new Error(`Video not found: ${video_id}`)
  }

  // Download the file
  const filePath = await downloadFile(supabase, video.storage_path)

  // Create ASR client based on backend
  const asrBackend = process.env.ASR_BACKEND || "hf"
  const whisperModel = process.env.WHISPER_MODEL || "small"

  let asrClient: AsrClient

  if (asrBackend === "hf") {
    if (!process.env.HF_API_TOKEN) {
      throw new Error("HF_API_TOKEN is required for Hugging Face backend")
    }
    asrClient = new HuggingFaceWhisperClient(process.env.HF_API_TOKEN, whisperModel)
  } else if (asrBackend === "local") {
    asrClient = new LocalWhisperClient(whisperModel)
  } else {
    throw new Error(`Unknown ASR backend: ${asrBackend}`)
  }

  // Transcribe the file
  logger.info({ msg: "Transcribing file", videoId: video_id, locale: video.locale })
  const transcription = await asrClient.transcribe(filePath, video.locale)

  // Save transcription to database
  const { error: transcriptionError } = await supabase.from("transcriptions").insert({
    video_id,
    raw: transcription.raw,
    text_clean: transcription.text,
    words: transcription.words,
    language: transcription.language,
  })

  if (transcriptionError) {
    throw transcriptionError
  }

  // Create VTT file
  const vttContent = createVttFile(transcription.words)
  const vttPath = `${video_id}/transcript.vtt`

  // Upload VTT file
  await uploadFile(supabase, "exports", vttPath, vttContent, "text/vtt")

  // Create export record
  await supabase.from("exports").insert({
    video_id,
    type: "vtt",
    storage_path: `exports/${vttPath}`,
  })

  // Update video status
  await supabase.from("videos").update({ status: "transcribed" }).eq("id", video_id)

  // Queue proxy job
  await supabase.from("processing_jobs").insert({
    video_id,
    type: "proxy",
    input: { source_path: video.storage_path },
  })

  // Update job output
  return {
    transcription_id: transcription.id,
    language: transcription.language,
    duration: transcription.duration,
  }
}
