{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:unit": "jest --testPathPattern=__tests__/unit", "test:integration": "jest --testPathPattern=__tests__/integration", "test:api": "jest --testPathPattern=__tests__/api", "test:components": "jest --testPathPattern=__tests__/components", "test:performance": "jest --testPathPattern=__tests__/performance", "test:accessibility": "jest --testPathPattern=__tests__/accessibility", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test:coverage && npm run test:e2e", "test:ci": "jest --coverage --watchAll=false --ci --reporters=default --reporters=jest-junit --maxWorkers=2", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:update-snapshots": "jest --updateSnapshot", "test:clear-cache": "jest --clear<PERSON>ache"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-toast": "^1.1.5", "@reality-scripts/lib": "workspace:*", "@reality-scripts/ui": "workspace:*", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.39.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "next": "15.0.0", "next-intl": "^3.3.2", "react": "^19", "react-dom": "^19", "tailwind-merge": "^2.0.0", "zod": "^3.22.4"}, "devDependencies": {"@axe-core/playwright": "^4.8.2", "@playwright/test": "^1.40.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.14", "@types/node": "^20.10.4", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "15.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "lighthouse": "^11.4.0", "msw": "^2.0.8", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1", "^@reality-scripts/(.*)$": "<rootDir>/../../packages/$1/src"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/app/layout.tsx", "!src/app/globals.css", "!src/**/*.stories.{js,jsx,ts,tsx}", "!src/**/*.test.{js,jsx,ts,tsx}", "!src/**/*.spec.{js,jsx,ts,tsx}"], "coverageReporters": ["text", "lcov", "html", "json-summary", "clover"], "coverageThreshold": {"global": {"branches": 85, "functions": 85, "lines": 85, "statements": 85}, "src/components/**/*.{js,jsx,ts,tsx}": {"branches": 90, "functions": 90, "lines": 90, "statements": 90}, "src/lib/**/*.{js,jsx,ts,tsx}": {"branches": 95, "functions": 95, "lines": 95, "statements": 95}}, "testTimeout": 10000, "maxWorkers": "50%"}}