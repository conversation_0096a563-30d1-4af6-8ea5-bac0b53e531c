/**
 * Language Support Integration Tests
 *
 * Comprehensive tests for internationalization features including
 * language switching, RTL support, and localization accuracy.
 */

import type React from "react"
import { render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { NextIntlClientProvider } from "next-intl"
import { LanguageSwitcher } from "@reality-scripts/ui"
import { LOCALE_CONFIGS, validateLocale, isRTL } from "@reality-scripts/lib"
import type { SupportedLocale } from "@reality-scripts/lib"

// Mock messages for testing
const mockMessages = {
  en: {
    common: {
      appName: "RealityScripts",
      login: "Login",
      register: "Register",
    },
  },
  ar: {
    common: {
      appName: "ريالتي سكريبتس",
      login: "تسجيل الدخول",
      register: "إنشاء حساب",
    },
  },
  es: {
    common: {
      appName: "RealityScripts",
      login: "Iniciar sesión",
      register: "Registrarse",
    },
  },
  fr: {
    common: {
      appName: "RealityScripts",
      login: "Se connecter",
      register: "S'inscrire",
    },
  },
}

// Test wrapper component
const TestWrapper: React.FC<{
  locale: SupportedLocale
  children: React.ReactNode
}> = ({ locale, children }) => (
  <NextIntlClientProvider
    locale={locale}
    messages={mockMessages[locale as keyof typeof mockMessages] || mockMessages.en}
  >
    <div dir={isRTL(locale) ? "rtl" : "ltr"}>{children}</div>
  </NextIntlClientProvider>
)

describe("Language Support Tests", () => {
  describe("Language Toggle Functionality", () => {
    it("should render language switcher with current locale", () => {
      // Arrange
      const currentLocale: SupportedLocale = "en"
      const onLocaleChange = jest.fn()

      // Act
      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Assert
      expect(screen.getByRole("button", { name: /select language/i })).toBeInTheDocument()
      expect(screen.getByText("🇺🇸")).toBeInTheDocument()
    })

    it("should open language dropdown when clicked", async () => {
      // Arrange
      const user = userEvent.setup()
      const currentLocale: SupportedLocale = "en"
      const onLocaleChange = jest.fn()

      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Act
      await user.click(screen.getByRole("button", { name: /select language/i }))

      // Assert
      await waitFor(() => {
        expect(screen.getByRole("listbox")).toBeInTheDocument()
      })
    })

    it("should display all available languages in dropdown", async () => {
      // Arrange
      const user = userEvent.setup()
      const currentLocale: SupportedLocale = "en"
      const onLocaleChange = jest.fn()

      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Act
      await user.click(screen.getByRole("button", { name: /select language/i }))

      // Assert
      await waitFor(() => {
        Object.values(LOCALE_CONFIGS).forEach((config) => {
          expect(screen.getByText(config.nativeName)).toBeInTheDocument()
        })
      })
    })

    it("should call onLocaleChange when language is selected", async () => {
      // Arrange
      const user = userEvent.setup()
      const currentLocale: SupportedLocale = "en"
      const onLocaleChange = jest.fn()

      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Act
      await user.click(screen.getByRole("button", { name: /select language/i }))
      await waitFor(() => {
        expect(screen.getByRole("listbox")).toBeInTheDocument()
      })
      await user.click(screen.getByText("Español"))

      // Assert
      expect(onLocaleChange).toHaveBeenCalledWith("es")
    })

    it("should close dropdown after language selection", async () => {
      // Arrange
      const user = userEvent.setup()
      const currentLocale: SupportedLocale = "en"
      const onLocaleChange = jest.fn()

      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Act
      await user.click(screen.getByRole("button", { name: /select language/i }))
      await waitFor(() => {
        expect(screen.getByRole("listbox")).toBeInTheDocument()
      })
      await user.click(screen.getByText("Español"))

      // Assert
      await waitFor(() => {
        expect(screen.queryByRole("listbox")).not.toBeInTheDocument()
      })
    })

    it("should handle keyboard navigation", async () => {
      // Arrange
      const user = userEvent.setup()
      const currentLocale: SupportedLocale = "en"
      const onLocaleChange = jest.fn()

      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Act
      const button = screen.getByRole("button", { name: /select language/i })
      await user.click(button)
      await user.keyboard("{Enter}")

      // Assert
      await waitFor(() => {
        expect(screen.getByRole("listbox")).toBeInTheDocument()
      })
    })
  })

  describe("Arabic RTL Support", () => {
    it("should apply RTL direction for Arabic locale", () => {
      // Arrange
      const currentLocale: SupportedLocale = "ar"
      const onLocaleChange = jest.fn()

      // Act
      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Assert
      const container = screen.getByRole("button", { name: /select language/i }).closest("div")
      expect(container).toHaveAttribute("dir", "rtl")
    })

    it("should display Arabic text correctly", () => {
      // Arrange
      const currentLocale: SupportedLocale = "ar"

      // Act
      render(
        <TestWrapper locale={currentLocale}>
          <div>{mockMessages.ar.common.appName}</div>
        </TestWrapper>,
      )

      // Assert
      expect(screen.getByText("ريالتي سكريبتس")).toBeInTheDocument()
    })

    it("should adjust layout for RTL languages", async () => {
      // Arrange
      const user = userEvent.setup()
      const currentLocale: SupportedLocale = "ar"
      const onLocaleChange = jest.fn()

      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Act
      await user.click(screen.getByRole("button", { name: /select language/i }))

      // Assert
      await waitFor(() => {
        const dropdown = screen.getByRole("listbox")
        expect(dropdown).toHaveClass("left-0") // RTL positioning
      })
    })

    it("should handle mixed LTR/RTL content correctly", () => {
      // Arrange
      const currentLocale: SupportedLocale = "ar"

      // Act
      render(
        <TestWrapper locale={currentLocale}>
          <div>
            <span>{mockMessages.ar.common.appName}</span>
            <span>English Text</span>
          </div>
        </TestWrapper>,
      )

      // Assert
      expect(screen.getByText("ريالتي سكريبتس")).toBeInTheDocument()
      expect(screen.getByText("English Text")).toBeInTheDocument()
    })
  })

  describe("Localization Validation", () => {
    it("should validate supported locales correctly", () => {
      // Test valid locales
      expect(validateLocale("en")).toBe("en")
      expect(validateLocale("ar")).toBe("ar")
      expect(validateLocale("es")).toBe("es")

      // Test invalid locales
      expect(validateLocale("invalid")).toBe("en") // Should fallback to default
      expect(validateLocale("")).toBe("en")
    })

    it("should detect RTL languages correctly", () => {
      // Test RTL languages
      expect(isRTL("ar")).toBe(true)

      // Test LTR languages
      expect(isRTL("en")).toBe(false)
      expect(isRTL("es")).toBe(false)
      expect(isRTL("fr")).toBe(false)
    })

    it("should load correct locale configuration", () => {
      // Test English configuration
      const enConfig = LOCALE_CONFIGS.en
      expect(enConfig.code).toBe("en")
      expect(enConfig.direction).toBe("ltr")
      expect(enConfig.nativeName).toBe("English")

      // Test Arabic configuration
      const arConfig = LOCALE_CONFIGS.ar
      expect(arConfig.code).toBe("ar")
      expect(arConfig.direction).toBe("rtl")
      expect(arConfig.nativeName).toBe("العربية")
    })

    it("should handle missing translations gracefully", () => {
      // Arrange
      const incompleteMessages = {
        common: {
          appName: "RealityScripts",
          // Missing other translations
        },
      }

      // Act
      render(
        <NextIntlClientProvider locale="en" messages={incompleteMessages}>
          <div>Test content</div>
        </NextIntlClientProvider>,
      )

      // Assert - Should not crash
      expect(screen.getByText("Test content")).toBeInTheDocument()
    })
  })

  describe("Language Persistence", () => {
    it("should maintain language selection across page reloads", () => {
      // This would typically involve testing localStorage or cookies
      // For now, we test the locale validation function
      const storedLocale = "es"
      const validatedLocale = validateLocale(storedLocale)
      expect(validatedLocale).toBe("es")
    })

    it("should handle invalid stored locale gracefully", () => {
      const invalidStoredLocale = "invalid-locale"
      const validatedLocale = validateLocale(invalidStoredLocale)
      expect(validatedLocale).toBe("en") // Should fallback to default
    })
  })

  describe("Accessibility in Multiple Languages", () => {
    it("should provide proper ARIA labels for different languages", async () => {
      // Arrange
      const user = userEvent.setup()
      const currentLocale: SupportedLocale = "ar"
      const onLocaleChange = jest.fn()

      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Act
      await user.click(screen.getByRole("button", { name: /select language/i }))

      // Assert
      await waitFor(() => {
        const listbox = screen.getByRole("listbox")
        expect(listbox).toHaveAttribute("aria-label", "Language options")
      })
    })

    it("should support keyboard navigation in RTL layout", async () => {
      // Arrange
      const user = userEvent.setup()
      const currentLocale: SupportedLocale = "ar"
      const onLocaleChange = jest.fn()

      render(
        <TestWrapper locale={currentLocale}>
          <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={onLocaleChange} />
        </TestWrapper>,
      )

      // Act
      const button = screen.getByRole("button", { name: /select language/i })
      button.focus()
      await user.keyboard("{Enter}")

      // Assert
      await waitFor(() => {
        expect(screen.getByRole("listbox")).toBeInTheDocument()
      })
    })
  })
})
