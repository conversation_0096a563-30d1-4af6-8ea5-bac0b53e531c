name: Production Deployment

on:
  push:
    branches: [main]
  workflow_dispatch:

env:
  NODE_VERSION: '20.x'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  audit-and-test:
    name: Security Audit & Testing
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Security audit
        run: npm audit --audit-level=high
      
      - name: Run linting
        run: npm run lint
      
      - name: Run type checking
        run: npx tsc --noEmit
      
      - name: Run comprehensive tests
        run: npm run test:ci
        env:
          CI: true
      
      - name: Upload test coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  build-and-deploy:
    name: Build and Deploy to Vercel
    runs-on: ubuntu-latest
    needs: audit-and-test
    steps:
      - uses: actions/checkout@v4
      
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      
      - name: Deploy Project Artifacts to Vercel
        id: deploy
        run: |
          url=$(vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }})
          echo "deployment-url=$url" >> $GITHUB_OUTPUT
      
      - name: Run post-deployment health check
        run: |
          sleep 30
          curl -f ${{ steps.deploy.outputs.deployment-url }}/api/health || exit 1
      
      - name: Comment deployment URL
        uses: actions/github-script@v6
        if: github.event_name == 'pull_request'
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '🚀 Deployed to: ${{ steps.deploy.outputs.deployment-url }}'
            })

  post-deployment:
    name: Post-Deployment Verification
    runs-on: ubuntu-latest
    needs: build-and-deploy
    steps:
      - name: Run smoke tests
        run: |
          echo "Running post-deployment smoke tests..."
          # Add your smoke tests here
          
      - name: Notify team
        if: success()
        run: |
          echo "✅ Production deployment successful!"
          echo "🔗 Application URL: https://realityscripts.vercel.app"
          echo "📊 Health check: https://realityscripts.vercel.app/api/health"
