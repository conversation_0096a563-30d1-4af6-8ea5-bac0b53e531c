-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- videos table
CREATE TABLE videos (
  id          UUID      PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id     UUID      NOT NULL REFERENCES auth.users(id),
  storage_path TEXT     NOT NULL,
  locale      VARCHAR(10) NOT NULL DEFAULT 'en',
  expires_at  TIMESTAMPTZ,
  status      TEXT      NOT NULL DEFAULT 'uploaded',
  created_at  TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- processing_jobs table
CREATE TABLE processing_jobs (
  id        UUID      PRIMARY KEY DEFAULT uuid_generate_v4(),
  video_id  UUID      NOT NULL REFERENCES videos(id),
  type      TEXT      NOT NULL,       -- 'transcribe','proxy','export'
  status    TEXT      NOT NULL DEFAULT 'queued',
  input     JSONB,
  output    JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- transcriptions table
CREATE TABLE transcriptions (
  id         UUID      PRIMARY KEY DEFAULT uuid_generate_v4(),
  video_id   UUID      NOT NULL REFERENCES videos(id),
  raw        JSONB     NOT NULL,
  text_clean TEXT      NOT NULL,
  words      JSONB     NOT NULL,
  language   VARCHAR(10) NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- exports table
CREATE TABLE exports (
  id           UUID      PRIMARY KEY DEFAULT uuid_generate_v4(),
  video_id     UUID      NOT NULL REFERENCES videos(id),
  type         TEXT      NOT NULL,      -- 'srt','vtt','txt','json'
  storage_path TEXT      NOT NULL,
  created_at   TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add updated_at column to videos table
ALTER TABLE videos ADD COLUMN updated_at TIMESTAMPTZ DEFAULT now();

-- Add indexes for better performance
CREATE INDEX idx_videos_user_id_created_at ON videos(user_id, created_at DESC);
CREATE INDEX idx_videos_status ON videos(status);
CREATE INDEX idx_videos_expires_at ON videos(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_processing_jobs_status_created_at ON processing_jobs(status, created_at);
CREATE INDEX idx_transcriptions_video_id ON transcriptions(video_id);
CREATE INDEX idx_exports_video_id_type ON exports(video_id, type);

-- Add constraints for data integrity
ALTER TABLE videos ADD CONSTRAINT videos_status_check 
  CHECK (status IN ('uploaded', 'processing', 'transcribed', 'processed', 'failed', 'expired'));

ALTER TABLE videos ADD CONSTRAINT videos_locale_check 
  CHECK (locale IN ('en', 'es', 'fr', 'de', 'ar', 'zh', 'ja'));

ALTER TABLE processing_jobs ADD CONSTRAINT processing_jobs_type_check 
  CHECK (type IN ('transcribe', 'proxy', 'export', 'cleanup'));

ALTER TABLE processing_jobs ADD CONSTRAINT processing_jobs_status_check 
  CHECK (status IN ('queued', 'processing', 'completed', 'failed'));

ALTER TABLE exports ADD CONSTRAINT exports_type_check 
  CHECK (type IN ('srt', 'vtt', 'txt', 'json'));

-- Create RLS policies
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE processing_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE transcriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE exports ENABLE ROW LEVEL SECURITY;

-- Enhanced RLS policies with better security
DROP POLICY IF EXISTS "Users manage their videos" ON videos;
CREATE POLICY "Users can view their own videos"
  ON videos FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own videos"
  ON videos FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own videos"
  ON videos FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own videos"
  ON videos FOR DELETE USING (user_id = auth.uid());

-- Users can manage their own processing jobs
CREATE POLICY "Users manage their processing jobs"
  ON processing_jobs FOR ALL USING (
    video_id IN (SELECT id FROM videos WHERE user_id = auth.uid())
  );

-- Users can manage their own transcriptions
CREATE POLICY "Users manage their transcriptions"
  ON transcriptions FOR ALL USING (
    video_id IN (SELECT id FROM videos WHERE user_id = auth.uid())
  );

-- Users can manage their own exports
CREATE POLICY "Users manage their exports"
  ON exports FOR ALL USING (
    video_id IN (SELECT id FROM videos WHERE user_id = auth.uid())
  );

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON videos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
