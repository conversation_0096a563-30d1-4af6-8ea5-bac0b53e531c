import { createClientComponentClient, createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import type { Database } from "@reality-scripts/lib"
import { env } from "./env"

/**
 * Client-side Supabase client for use in React components
 * Automatically handles authentication state and session management
 */
export const createClient = () => {
  return createClientComponentClient<Database>({
    supabaseUrl: env.SUPABASE_URL,
    supabaseKey: env.SUPABASE_ANON_KEY,
  })
}

/**
 * Server-side Supabase client for use in Server Components
 * Handles server-side authentication and session management
 */
export const createServerClient = () => {
  return createServerComponentClient<Database>({
    cookies,
    supabaseUrl: env.SUPABASE_URL,
    supabaseKey: env.SUPABASE_ANON_KEY,
  })
}

/**
 * Route handler Supabase client for use in API routes
 * Handles authentication in API endpoints
 */
export const createRouteClient = () => {
  return createRouteHandlerClient<Database>({
    cookies,
    supabaseUrl: env.SUPABASE_URL,
    supabaseKey: env.SUPABASE_ANON_KEY,
  })
}

/**
 * Service role client for administrative operations
 * Should only be used on the server side for privileged operations
 */
export const createServiceClient = () => {
  if (typeof window !== "undefined") {
    throw new Error("Service client should only be used on the server side")
  }

  return createRouteHandlerClient<Database>({
    cookies,
    supabaseUrl: env.SUPABASE_URL,
    supabaseKey: env.SUPABASE_SERVICE_ROLE_KEY,
  })
}

/**
 * Utility function to check if user is authenticated
 */
export async function getAuthenticatedUser() {
  const supabase = createServerClient()
  const {
    data: { session },
    error,
  } = await supabase.auth.getSession()

  if (error || !session) {
    return null
  }

  return session.user
}

/**
 * Utility function to require authentication
 * Throws an error if user is not authenticated
 */
export async function requireAuth() {
  const user = await getAuthenticatedUser()

  if (!user) {
    throw new Error("Authentication required")
  }

  return user
}
