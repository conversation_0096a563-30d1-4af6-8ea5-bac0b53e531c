import { createLogger } from "./logger"

const logger = createLogger("health")

export interface HealthCheck {
  name: string
  status: "healthy" | "unhealthy" | "degraded"
  message?: string
  timestamp: Date
  responseTime?: number
}

export interface SystemHealth {
  status: "healthy" | "unhealthy" | "degraded"
  checks: HealthCheck[]
  timestamp: Date
}

class HealthMonitor {
  private checks: Map<string, () => Promise<HealthCheck>> = new Map()

  registerCheck(name: string, checkFn: () => Promise<HealthCheck>) {
    this.checks.set(name, checkFn)
    logger.info({ checkName: name }, "Health check registered")
  }

  async runCheck(name: string): Promise<HealthCheck> {
    const checkFn = this.checks.get(name)
    if (!checkFn) {
      return {
        name,
        status: "unhealthy",
        message: "Check not found",
        timestamp: new Date(),
      }
    }

    const startTime = Date.now()
    try {
      const result = await checkFn()
      const responseTime = Date.now() - startTime
      return {
        ...result,
        responseTime,
      }
    } catch (error) {
      const responseTime = Date.now() - startTime
      logger.error({ error, checkName: name }, "Health check failed")
      return {
        name,
        status: "unhealthy",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date(),
        responseTime,
      }
    }
  }

  async runAllChecks(): Promise<SystemHealth> {
    const checkPromises = Array.from(this.checks.keys()).map((name) => this.runCheck(name))
    const checks = await Promise.all(checkPromises)

    const hasUnhealthy = checks.some((check) => check.status === "unhealthy")
    const hasDegraded = checks.some((check) => check.status === "degraded")

    let status: SystemHealth["status"] = "healthy"
    if (hasUnhealthy) {
      status = "unhealthy"
    } else if (hasDegraded) {
      status = "degraded"
    }

    return {
      status,
      checks,
      timestamp: new Date(),
    }
  }
}

export const healthMonitor = new HealthMonitor()
