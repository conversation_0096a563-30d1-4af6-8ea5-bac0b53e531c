import type { SupabaseClient } from "@supabase/supabase-js"
import { uploadFile } from "../utils/storage"
import { createSrtFile } from "../utils/srt"

// Process export job
export async function exportJob(supabase: SupabaseClient, job: any) {
  const { video_id, input } = job
  const format = input?.format

  if (!format) {
    throw new Error("Export format is required")
  }

  // Get video details
  const { data: video, error: videoError } = await supabase.from("videos").select("*").eq("id", video_id).single()

  if (videoError || !video) {
    throw new Error(`Video not found: ${video_id}`)
  }

  // Get transcription
  const { data: transcription, error: transcriptionError } = await supabase
    .from("transcriptions")
    .select("*")
    .eq("video_id", video_id)
    .single()

  if (transcriptionError || !transcription) {
    throw new Error(`Transcription not found for video: ${video_id}`)
  }

  // Create export based on format
  let content = ""
  let contentType = ""
  let fileName = ""

  switch (format) {
    case "srt":
      content = createSrtFile(transcription.words)
      contentType = "application/x-subrip"
      fileName = "transcript.srt"
      break
    case "txt":
      content = transcription.text_clean
      contentType = "text/plain"
      fileName = "transcript.txt"
      break
    default:
      throw new Error(`Unsupported export format: ${format}`)
  }

  // Upload export file
  const filePath = `${video_id}/${fileName}`
  await uploadFile(supabase, "exports", filePath, content, contentType)

  // Create export record
  await supabase.from("exports").insert({
    video_id,
    type: format,
    storage_path: `exports/${filePath}`,
  })

  // Update job output
  return {
    format,
    path: filePath,
  }
}
