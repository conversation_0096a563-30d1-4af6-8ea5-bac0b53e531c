import { NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { metrics, createLogger } from "@reality-scripts/lib"

const logger = createLogger("metrics-api")

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Check if user is authenticated and has admin role
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get metrics
    const currentMetrics = metrics.getMetrics()

    logger.info({ metricsCount: currentMetrics.length }, "Metrics requested")

    return NextResponse.json({
      metrics: currentMetrics,
      timestamp: new Date(),
    })
  } catch (error) {
    logger.error({ error }, "Failed to get metrics")
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
