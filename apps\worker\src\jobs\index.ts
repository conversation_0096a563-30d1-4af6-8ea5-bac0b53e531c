import type { SupabaseClient } from "@supabase/supabase-js"
import { logger } from "../utils/logger"
import { transcribeJob } from "./transcribe"
import { proxyJob } from "./proxy"
import { exportJob } from "./export"

// Process pending jobs
export async function processJobs(supabase: SupabaseClient) {
  try {
    // Get the next pending job
    const { data: job, error } = await supabase
      .from("processing_jobs")
      .select("*")
      .eq("status", "queued")
      .order("created_at", { ascending: true })
      .limit(1)
      .single()

    if (error || !job) {
      // No jobs to process
      return
    }

    logger.info({ msg: "Processing job", jobId: job.id, type: job.type })

    // Update job status to processing
    await supabase.from("processing_jobs").update({ status: "processing" }).eq("id", job.id)

    try {
      // Process job based on type
      switch (job.type) {
        case "transcribe":
          await transcribe<PERSON>ob(supabase, job)
          break
        case "proxy":
          await proxy<PERSON>ob(supabase, job)
          break
        case "export":
          await exportJob(supabase, job)
          break
        default:
          throw new Error(`Unknown job type: ${job.type}`)
      }

      // Update job status to completed
      await supabase.from("processing_jobs").update({ status: "completed" }).eq("id", job.id)

      logger.info({ msg: "Job completed", jobId: job.id })
    } catch (error) {
      logger.error({ msg: "Job failed", jobId: job.id, error })

      // Update job status to failed
      await supabase
        .from("processing_jobs")
        .update({
          status: "failed",
          output: { error: error instanceof Error ? error.message : String(error) },
        })
        .eq("id", job.id)
    }
  } catch (error) {
    logger.error({ msg: "Error processing jobs", error })
  }
}
