-- Create function to clean up expired videos
CREATE OR REPLACE FUNCTION cleanup_expired_videos()
R<PERSON><PERSON>NS void AS $$
DECLARE
  video_record RECORD;
BEGIN
  FOR video_record IN
    SELECT id, storage_path
    FROM videos
    WHERE expires_at < NOW() AND expires_at IS NOT NULL
  LOOP
    -- Delete the raw video file from storage
    -- Note: This is a placeholder. In production, you'd use Supabase's storage API
    -- or a scheduled job to handle this deletion.
    
    -- Update the video status
    UPDATE videos
    SET status = 'expired'
    WHERE id = video_record.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create a cron job to run the cleanup function hourly
-- Note: This requires pg_cron extension to be enabled
-- CREATE EXTENSION IF NOT EXISTS pg_cron;
-- SELECT cron.schedule('0 * * * *', 'SELECT cleanup_expired_videos()');
