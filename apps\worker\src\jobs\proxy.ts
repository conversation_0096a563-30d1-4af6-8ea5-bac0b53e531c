import type { SupabaseClient } from "@supabase/supabase-js"
import { logger } from "../utils/logger"
import { downloadFile, uploadFileFromPath } from "../utils/storage"
import { createVideoProxy } from "../utils/ffmpeg"

// Process proxy creation job
export async function proxyJob(supabase: SupabaseClient, job: any) {
  const { video_id, input } = job

  // Get video details
  const { data: video, error: videoError } = await supabase.from("videos").select("*").eq("id", video_id).single()

  if (videoError || !video) {
    throw new Error(`Video not found: ${video_id}`)
  }

  // Download the file
  const filePath = await downloadFile(supabase, video.storage_path)

  // Create proxy
  logger.info({ msg: "Creating proxy", videoId: video_id })
  const proxyPath = await createVideoProxy(filePath)

  // Upload proxy
  const storagePath = video.storage_path.replace("raw/", "")
  await uploadFileFromPath(supabase, "proxy", storagePath, proxyPath, "video/mp4")

  // Update video status
  await supabase.from("videos").update({ status: "processed" }).eq("id", video_id)

  // Queue export jobs
  await supabase.from("processing_jobs").insert([
    {
      video_id,
      type: "export",
      input: { format: "srt" },
    },
    {
      video_id,
      type: "export",
      input: { format: "txt" },
    },
  ])

  // Update job output
  return {
    proxy_path: `proxy/${storagePath}`,
  }
}
