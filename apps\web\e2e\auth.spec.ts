/**
 * Authentication End-to-End Tests
 *
 * Tests the complete authentication flow including login, registration,
 * and session management across the application.
 */

import { test, expect } from "@playwright/test"

test.describe("Authentication Flow", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto("/")
  })

  test("should allow user to register and login", async ({ page }) => {
    // Navigate to registration page
    await page.click("text=Register")
    await expect(page).toHaveURL(/.*register/)

    // Fill registration form
    await page.fill('input[type="email"]', "<EMAIL>")
    await page.fill('input[type="password"]', "password123")
    await page.click('button[type="submit"]')

    // Should redirect to login with success message
    await expect(page).toHaveURL(/.*login/)

    // Login with the new account
    await page.fill('input[type="email"]', "<EMAIL>")
    await page.fill('input[type="password"]', "password123")
    await page.click('button[type="submit"]')

    // Should redirect to dashboard
    await expect(page).toHaveURL(/.*dashboard/)
    await expect(page.locator("h1")).toContainText("Your Videos")
  })

  test("should handle login errors gracefully", async ({ page }) => {
    // Navigate to login page
    await page.click("text=Login")

    // Try to login with invalid credentials
    await page.fill('input[type="email"]', "<EMAIL>")
    await page.fill('input[type="password"]', "wrongpassword")
    await page.click('button[type="submit"]')

    // Should show error message
    await expect(page.locator('[role="alert"]')).toBeVisible()
    await expect(page.locator('[role="alert"]')).toContainText("error")
  })

  test("should redirect unauthenticated users", async ({ page }) => {
    // Try to access protected route
    await page.goto("/dashboard")

    // Should redirect to login
    await expect(page).toHaveURL(/.*login/)
  })

  test("should handle logout correctly", async ({ page }) => {
    // Mock authenticated state
    await page.goto("/dashboard")

    // Assuming we're logged in, click logout
    await page.click('button:has-text("Logout")')

    // Should redirect to home page
    await expect(page).toHaveURL("/")
  })
})
