import pino from "pino"

// Create a structured logger for the application
export const logger = pino({
  level: process.env.LOG_LEVEL || "info",
  transport:
    process.env.NODE_ENV === "development"
      ? {
          target: "pino-pretty",
          options: {
            colorize: true,
            translateTime: "SYS:standard",
            ignore: "pid,hostname",
          },
        }
      : undefined,
  formatters: {
    level: (label) => {
      return { level: label }
    },
  },
  timestamp: pino.stdTimeFunctions.isoTime,
})

// Application-specific logging utilities
export const createLogger = (component: string) => {
  return logger.child({ component })
}

// Error tracking utility
export const trackError = (error: Error, context?: Record<string, any>) => {
  logger.error(
    {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name,
      },
      context,
    },
    "Application error occurred",
  )
}

// Performance monitoring utility
export const trackPerformance = (operation: string, duration: number, metadata?: Record<string, any>) => {
  logger.info(
    {
      operation,
      duration,
      metadata,
    },
    "Performance metric",
  )
}
