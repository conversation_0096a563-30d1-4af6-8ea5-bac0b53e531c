/**
 * Production configuration for RealityScripts platform
 * Centralized configuration management for deployment
 */

import { env } from "./env"

export const config = {
  // Application metadata
  app: {
    name: "RealityScripts",
    version: "1.0.0",
    description: "Scalable Video Transcription & Intelligence Platform",
    url: env.IS_PRODUCTION ? "https://realityscripts.vercel.app" : "http://localhost:3000",
  },

  // Supabase configuration
  supabase: {
    url: env.SUPABASE_URL,
    anonKey: env.SUPABASE_ANON_KEY,
    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,
    maxConnections: env.IS_PRODUCTION ? 20 : 5,
    connectionTimeout: 30000,
  },

  // File upload limits
  upload: {
    maxFileSize: 1024 * 1024 * 1024, // 1GB
    allowedMimeTypes: [
      "video/mp4",
      "video/quicktime",
      "video/avi",
      "video/webm",
      "audio/mpeg",
      "audio/ogg",
      "audio/wav",
      "audio/mp3",
    ],
    allowedExtensions: ["mp4", "mov", "avi", "webm", "mp3", "ogg", "wav"],
  },

  // TTL configuration
  retention: {
    defaultTTL: 72 * 60 * 60 * 1000, // 72 hours in milliseconds
    maxTTL: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    warningThreshold: 12 * 60 * 60 * 1000, // 12 hours in milliseconds
  },

  // ASR configuration
  asr: {
    backend: env.ASR_BACKEND,
    model: env.WHISPER_MODEL,
    timeout: 300000, // 5 minutes
    retryAttempts: 3,
  },

  // Monitoring configuration
  monitoring: {
    logLevel: env.LOG_LEVEL,
    enableMetrics: env.IS_PRODUCTION,
    healthCheckInterval: 30000, // 30 seconds
  },

  // Security configuration
  security: {
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    corsOrigins: env.IS_PRODUCTION
      ? ["https://realityscripts.vercel.app"]
      : ["http://localhost:3000", "http://127.0.0.1:3000"],
  },

  // Feature flags
  features: {
    enableRealtime: true,
    enableAnalytics: env.IS_PRODUCTION,
    enablePerformanceMonitoring: env.IS_PRODUCTION,
    enableErrorTracking: true,
  },

  // Rate limiting
  rateLimit: {
    upload: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: env.IS_PRODUCTION ? 10 : 100, // requests per window
    },
    api: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: env.IS_PRODUCTION ? 100 : 1000, // requests per window
    },
  },
} as const

// Validate configuration on startup
export function validateConfig() {
  const requiredFields = [config.supabase.url, config.supabase.anonKey, config.supabase.serviceRoleKey]

  const missingFields = requiredFields.filter((field) => !field)

  if (missingFields.length > 0) {
    throw new Error(`Missing required configuration fields: ${missingFields.length} fields`)
  }

  console.log("✅ Configuration validated successfully")
}

// Initialize configuration
if (env.IS_PRODUCTION) {
  validateConfig()
}
