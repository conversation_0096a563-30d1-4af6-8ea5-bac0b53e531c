/**
 * Data Transformation Utilities
 *
 * Core functions for transforming data between different formats
 * and validating data integrity throughout the application.
 */

import { LOCALE_CONFIGS } from "@reality-scripts/lib"

export function formatTime(seconds: number, includeMilliseconds = false): string {
  if (isNaN(seconds) || seconds < 0 || !isFinite(seconds)) {
    return includeMilliseconds ? "0:00.000" : "0:00"
  }

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  const milliseconds = Math.floor((seconds % 1) * 1000)

  const formattedTime = `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`

  if (includeMilliseconds) {
    return `${formattedTime}.${milliseconds.toString().padStart(3, "0")}`
  }

  return formattedTime
}

export function parseVideoMetadata(file: File) {
  const extension = file.name.split(".").pop()?.toLowerCase() || ""

  return {
    name: file.name,
    type: file.type,
    size: file.size,
    lastModified: new Date(file.lastModified),
    extension,
    isVideo: file.type.startsWith("video/"),
    isAudio: file.type.startsWith("audio/"),
  }
}

export function transformTranscriptionData(rawTranscription: any) {
  const words =
    rawTranscription.segments?.flatMap(
      (segment: any) =>
        segment.words?.map((word: any) => ({
          text: word.word || word.text,
          start: Math.round((word.start || 0) * 1000), // Convert to milliseconds
          end: Math.round((word.end || 0) * 1000),
          confidence: word.confidence || 1.0,
        })) || [],
    ) || []

  const segments =
    rawTranscription.segments?.map((segment: any) => ({
      start: Math.round((segment.start || 0) * 1000),
      end: Math.round((segment.end || 0) * 1000),
      text: segment.text || "",
    })) || []

  const duration = segments.length > 0 ? Math.max(...segments.map((s: any) => s.end)) : 0

  return {
    text: rawTranscription.text || "",
    words,
    segments,
    language: rawTranscription.language || "en",
    duration,
  }
}

export function validateUploadData(uploadData: any) {
  const errors: string[] = []
  const sanitizedData: any = {}

  // Validate file
  if (!uploadData.file || !(uploadData.file instanceof File)) {
    errors.push("File is required")
  } else {
    const validTypes = [
      "video/mp4",
      "video/quicktime",
      "video/x-msvideo",
      "audio/mpeg",
      "audio/ogg",
      "audio/wav",
      "audio/mp4",
    ]

    if (!validTypes.includes(uploadData.file.type)) {
      errors.push("Invalid file type")
    }

    if (uploadData.file.size > 1024 * 1024 * 1024) {
      // 1GB limit
      errors.push("File size exceeds limit")
    }

    sanitizedData.file = uploadData.file
  }

  // Validate locale
  if (uploadData.locale && !Object.keys(LOCALE_CONFIGS).includes(uploadData.locale)) {
    errors.push("Invalid locale")
  } else {
    sanitizedData.locale = uploadData.locale || "en"
  }

  // Sanitize title
  if (uploadData.title) {
    sanitizedData.title = sanitizeUserInput(uploadData.title)
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedData,
  }
}

export function sanitizeUserInput(input: string): string {
  if (typeof input !== "string") return ""

  // Remove HTML tags and trim whitespace
  return input
    .replace(/<[^>]*>/g, "") // Remove HTML tags
    .replace(/[<>]/g, "") // Remove remaining angle brackets
    .trim()
}

export function convertTimestamps(
  timestamps: number[],
  fromUnit: "seconds" | "milliseconds",
  toUnit: "seconds" | "milliseconds",
): number[] {
  if (fromUnit === toUnit) return timestamps

  return timestamps.map((timestamp) => {
    if (isNaN(timestamp) || timestamp < 0 || !isFinite(timestamp)) {
      return 0
    }

    if (fromUnit === "seconds" && toUnit === "milliseconds") {
      return timestamp * 1000
    } else if (fromUnit === "milliseconds" && toUnit === "seconds") {
      return timestamp / 1000
    }

    return timestamp
  })
}

export function generateExportData(transcription: any, format: string): string {
  switch (format.toLowerCase()) {
    case "srt":
      return generateSRT(transcription)
    case "vtt":
      return generateVTT(transcription)
    case "txt":
      return transcription.text || ""
    case "json":
      return JSON.stringify(transcription, null, 2)
    default:
      throw new Error(`Unsupported export format: ${format}`)
  }
}

function generateSRT(transcription: any): string {
  if (!transcription.words || transcription.words.length === 0) {
    return ""
  }

  const lines: string[] = []
  let cueIndex = 1

  // Group words into cues (max 10 words per cue)
  for (let i = 0; i < transcription.words.length; i += 10) {
    const cueWords = transcription.words.slice(i, i + 10)
    const startTime = formatSRTTime(cueWords[0].start)
    const endTime = formatSRTTime(cueWords[cueWords.length - 1].end)
    const text = cueWords.map((w: any) => w.text).join(" ")

    lines.push(cueIndex.toString(), `${startTime} --> ${endTime}`, text, "")

    cueIndex++
  }

  return lines.join("\n")
}

function generateVTT(transcription: any): string {
  if (!transcription.words || transcription.words.length === 0) {
    return "WEBVTT\n\n"
  }

  const lines = ["WEBVTT", ""]

  // Group words into cues
  for (let i = 0; i < transcription.words.length; i += 10) {
    const cueWords = transcription.words.slice(i, i + 10)
    const startTime = formatVTTTime(cueWords[0].start)
    const endTime = formatVTTTime(cueWords[cueWords.length - 1].end)
    const text = cueWords.map((w: any) => w.text).join(" ")

    lines.push(`${startTime} --> ${endTime}`, text, "")
  }

  return lines.join("\n")
}

function formatSRTTime(ms: number): string {
  const totalSeconds = Math.floor(ms / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  const milliseconds = ms % 1000

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")},${milliseconds.toString().padStart(3, "0")}`
}

function formatVTTTime(ms: number): string {
  const totalSeconds = Math.floor(ms / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  const milliseconds = ms % 1000

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${milliseconds.toString().padStart(3, "0")}`
}
