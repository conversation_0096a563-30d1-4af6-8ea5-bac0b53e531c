FROM node:18-alpine AS builder

# Install build dependencies
RUN apk add --no-cache python3 make g++ ffmpeg

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json turbo.json ./
COPY apps/worker/package.json ./apps/worker/
COPY packages/lib/package.json ./packages/lib/

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Build the app
RUN npm run build --workspace=worker

# Production image
FROM node:18-alpine

# Install runtime dependencies
RUN apk add --no-cache python3 py3-pip ffmpeg
RUN pip3 install --no-cache-dir whisper

# Set working directory
WORKDIR /app

# Copy built files
COPY --from=builder /app/apps/worker/dist ./dist
COPY --from=builder /app/apps/worker/package.json ./
COPY --from=builder /app/node_modules ./node_modules

# Set environment variables
ENV NODE_ENV=production

# Run the app
CMD ["node", "dist/index.js"]
