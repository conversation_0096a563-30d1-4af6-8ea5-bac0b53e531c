import path from "path"
import { tmpdir } from "os"
import { v4 as uuidv4 } from "uuid"
import ffmpeg from "fluent-ffmpeg"
import ffmpegStatic from "ffmpeg-static"
import { logger } from "./logger"

// Set ffmpeg path
ffmpeg.setFfmpegPath(ffmpegStatic as string)

// Extract audio from video
export async function extractAudio(videoPath: string): Promise<string> {
  try {
    // Create temp directory if it doesn't exist
    const tempDir = path.join(tmpdir(), "reality-scripts")

    // Generate output path
    const outputPath = path.join(tempDir, `${uuidv4()}.mp3`)

    // Extract audio
    return new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .noVideo()
        .audioCodec("libmp3lame")
        .audioBitrate("128k")
        .output(outputPath)
        .on("end", () => {
          logger.info({ msg: "Audio extraction complete", outputPath })
          resolve(outputPath)
        })
        .on("error", (err) => {
          logger.error({ msg: "Audio extraction failed", error: err })
          reject(err)
        })
        .run()
    })
  } catch (error) {
    logger.error({ msg: "Failed to extract audio", videoPath, error })
    throw error
  }
}

// Create low-res proxy video
export async function createVideoProxy(videoPath: string): Promise<string> {
  try {
    // Create temp directory if it doesn't exist
    const tempDir = path.join(tmpdir(), "reality-scripts")

    // Generate output path
    const outputPath = path.join(tempDir, `${uuidv4()}.mp4`)

    // Create proxy
    return new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .size("426x240") // 240p
        .videoBitrate("300k")
        .audioCodec("aac")
        .audioBitrate("64k")
        .format("mp4")
        .output(outputPath)
        .on("end", () => {
          logger.info({ msg: "Proxy creation complete", outputPath })
          resolve(outputPath)
        })
        .on("error", (err) => {
          logger.error({ msg: "Proxy creation failed", error: err })
          reject(err)
        })
        .run()
    })
  } catch (error) {
    logger.error({ msg: "Failed to create proxy", videoPath, error })
    throw error
  }
}
