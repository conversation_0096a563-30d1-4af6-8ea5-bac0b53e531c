"use client"

import type * as React from "react"
import { useState, useRef, useEffect } from "react"
import { ChevronDown, Globe } from "lucide-react"
import type { SupportedLocale, LocaleConfig } from "@reality-scripts/lib"
import { LOCALE_CONFIGS, isRTL } from "@reality-scripts/lib"

export interface LanguageSwitcherProps {
  currentLocale: SupportedLocale
  onLocaleChange: (locale: SupportedLocale) => void
  className?: string
  variant?: "compact" | "full"
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  currentLocale,
  onLocaleChange,
  className = "",
  variant = "compact",
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const currentConfig = LOCALE_CONFIGS[currentLocale]
  const isCurrentRTL = isRTL(currentLocale)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleLocaleSelect = (locale: SupportedLocale) => {
    onLocaleChange(locale)
    setIsOpen(false)
  }

  const handleKeyDown = (event: React.KeyboardEvent, locale?: SupportedLocale) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault()
      if (locale) {
        handleLocaleSelect(locale)
      } else {
        setIsOpen(!isOpen)
      }
    } else if (event.key === "Escape") {
      setIsOpen(false)
    }
  }

  return (
    <div
      className={`relative inline-block text-left ${className}`}
      ref={dropdownRef}
      dir={isCurrentRTL ? "rtl" : "ltr"}
    >
      <button
        type="button"
        className={`
          inline-flex items-center justify-center w-full rounded-md border border-gray-300 
          shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 
          hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 
          focus:ring-indigo-500 transition-colors duration-200
          ${isCurrentRTL ? "flex-row-reverse" : ""}
        `}
        onClick={() => setIsOpen(!isOpen)}
        onKeyDown={(e) => handleKeyDown(e)}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-label="Select language"
      >
        <Globe className={`h-4 w-4 ${variant === "full" ? (isCurrentRTL ? "ml-2" : "mr-2") : ""}`} />
        {variant === "full" && (
          <span className={isCurrentRTL ? "mr-2" : "ml-2"}>
            {currentConfig.flag} {currentConfig.nativeName}
          </span>
        )}
        {variant === "compact" && <span className={isCurrentRTL ? "mr-1" : "ml-1"}>{currentConfig.flag}</span>}
        <ChevronDown
          className={`
            h-4 w-4 transition-transform duration-200 
            ${isOpen ? "rotate-180" : ""} 
            ${isCurrentRTL ? "mr-1" : "ml-1"}
          `}
        />
      </button>

      {isOpen && (
        <div
          className={`
            absolute z-50 mt-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5
            ${isCurrentRTL ? "left-0" : "right-0"}
          `}
          role="listbox"
          aria-label="Language options"
        >
          <div className="py-1 max-h-60 overflow-auto">
            {Object.values(LOCALE_CONFIGS).map((config: LocaleConfig) => (
              <button
                key={config.code}
                type="button"
                className={`
                  w-full text-left px-4 py-2 text-sm transition-colors duration-150
                  hover:bg-gray-100 focus:bg-gray-100 focus:outline-none
                  ${config.code === currentLocale ? "bg-indigo-50 text-indigo-900" : "text-gray-900"}
                  ${config.direction === "rtl" ? "text-right" : "text-left"}
                `}
                onClick={() => handleLocaleSelect(config.code)}
                onKeyDown={(e) => handleKeyDown(e, config.code)}
                role="option"
                aria-selected={config.code === currentLocale}
                dir={config.direction}
              >
                <div className={`flex items-center ${config.direction === "rtl" ? "flex-row-reverse" : ""}`}>
                  <span className={`text-lg ${config.direction === "rtl" ? "ml-3" : "mr-3"}`}>{config.flag}</span>
                  <div className={config.direction === "rtl" ? "text-right" : "text-left"}>
                    <div className="font-medium">{config.nativeName}</div>
                    <div className="text-xs text-gray-500">{config.name}</div>
                  </div>
                  {config.code === currentLocale && (
                    <span className={`text-indigo-600 ${config.direction === "rtl" ? "mr-auto" : "ml-auto"}`}>✓</span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
