interface MetricData {
  name: string
  value: number
  tags?: Record<string, string>
  timestamp?: Date
}

class MetricsCollector {
  private metrics: MetricData[] = []

  increment(name: string, tags?: Record<string, string>) {
    this.record(name, 1, tags)
  }

  record(name: string, value: number, tags?: Record<string, string>) {
    this.metrics.push({
      name,
      value,
      tags,
      timestamp: new Date(),
    })
  }

  timing(name: string, startTime: number, tags?: Record<string, string>) {
    const duration = Date.now() - startTime
    this.record(name, duration, tags)
  }

  getMetrics(): MetricData[] {
    return [...this.metrics]
  }

  clearMetrics() {
    this.metrics = []
  }
}

export const metrics = new MetricsCollector()

// Utility function to measure execution time
export const measureTime = async <T>(
  operation: string,
  fn: () => Promise<T>,
  tags?: Record<string, string>
): Promise<T> => {
  const startTime = Date.now()
  try {
    const result = await fn()
    metrics.timing(`${operation}.success`, startTime, tags)
    return result
  } catch (error) {
    metrics.timing(`${operation}.error`, startTime, tags)
    throw error
  }
}
