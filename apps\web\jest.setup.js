"use client"

/**
 * Jest Setup File for RealityScripts Testing
 *
 * This file configures the testing environment with necessary polyfills,
 * mocks, and global test utilities for comprehensive testing coverage.
 */

import "@testing-library/jest-dom"

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return "/"
  },
}))

// Mock Next.js dynamic imports
jest.mock("next/dynamic", () => (func) => {
  const DynamicComponent = (props) => {
    const Component = func()
    return <Component {...props} />
  }
  DynamicComponent.displayName = "DynamicComponent"
  return DynamicComponent
})

// Mock Supabase client
jest.mock("@supabase/auth-helpers-nextjs", () => ({
  createClientComponentClient: jest.fn(() => ({
    auth: {
      getSession: jest.fn(),
      getUser: jest.fn(),
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn(),
    })),
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(),
        download: jest.fn(),
        createSignedUrl: jest.fn(),
      })),
    },
  })),
  createServerComponentClient: jest.fn(),
  createRouteHandlerClient: jest.fn(),
}))

// Mock window.matchMedia for responsive design tests
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock HTMLMediaElement methods
Object.defineProperty(HTMLMediaElement.prototype, "play", {
  writable: true,
  value: jest.fn().mockImplementation(() => Promise.resolve()),
})

Object.defineProperty(HTMLMediaElement.prototype, "pause", {
  writable: true,
  value: jest.fn(),
})

Object.defineProperty(HTMLMediaElement.prototype, "load", {
  writable: true,
  value: jest.fn(),
})

// Global test utilities
global.testUtils = {
  // Mock user session
  mockUserSession: {
    user: {
      id: "test-user-id",
      email: "<EMAIL>",
      created_at: "2023-01-01T00:00:00Z",
    },
    access_token: "mock-access-token",
    refresh_token: "mock-refresh-token",
  },

  // Mock video data
  mockVideoData: {
    id: "test-video-id",
    user_id: "test-user-id",
    storage_path: "raw/test-user-id/test-video.mp4",
    locale: "en",
    expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000).toISOString(),
    status: "transcribed",
    created_at: "2023-01-01T00:00:00Z",
  },

  // Mock transcription data
  mockTranscriptionData: {
    id: "test-transcription-id",
    video_id: "test-video-id",
    raw: { segments: [] },
    text_clean: "This is a test transcription.",
    words: [
      { text: "This", start: 0, end: 500 },
      { text: "is", start: 500, end: 750 },
      { text: "a", start: 750, end: 900 },
      { text: "test", start: 900, end: 1400 },
      { text: "transcription.", start: 1400, end: 2000 },
    ],
    language: "en",
    created_at: "2023-01-01T00:00:00Z",
  },
}
