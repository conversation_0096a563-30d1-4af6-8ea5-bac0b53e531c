import { type NextRequest, NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(request: NextRequest, { params }: { params: { id: string; format: string } }) {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Check if user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if video exists and belongs to user
    const { data: video, error: videoError } = await supabase
      .from("videos")
      .select("*")
      .eq("id", params.id)
      .eq("user_id", session.user.id)
      .single()

    if (videoError || !video) {
      return NextResponse.json({ error: "Video not found" }, { status: 404 })
    }

    // Get transcription
    const { data: transcription, error: transcriptionError } = await supabase
      .from("transcriptions")
      .select("*")
      .eq("video_id", params.id)
      .single()

    if (transcriptionError || !transcription) {
      return NextResponse.json({ error: "Transcription not found" }, { status: 404 })
    }

    // Check if export already exists
    const { data: existingExport } = await supabase
      .from("exports")
      .select("storage_path")
      .eq("video_id", params.id)
      .eq("type", params.format)
      .single()

    if (existingExport) {
      // Get signed URL for existing export
      const { data: urlData, error: urlError } = await supabase.storage
        .from("exports")
        .createSignedUrl(existingExport.storage_path.replace("exports/", ""), 3600)

      if (urlError) {
        throw urlError
      }

      return NextResponse.redirect(urlData.signedUrl)
    }

    // If export doesn't exist, return error (exports should be created by worker)
    return NextResponse.json({ error: "Export not found" }, { status: 404 })
  } catch (error) {
    console.error("Error getting export:", error)
    return NextResponse.json({ error: "Failed to get export" }, { status: 500 })
  }
}
