import dotenv from "dotenv"
import { createClient } from "@supabase/supabase-js"
import { logger } from "./utils/logger"
import { processJobs } from "./jobs"

// Load environment variables
dotenv.config()

// Validate required environment variables
const requiredEnvVars = ["SUPABASE_URL", "SUPABASE_SERVICE_ROLE_KEY", "ASR_BACKEND", "WHISPER_MODEL"]

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    logger.error(`Missing required environment variable: ${envVar}`)
    process.exit(1)
  }
}

// Create Supabase client
const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!)

// Start processing jobs
async function main() {
  logger.info({
    msg: "Worker starting",
    asrBackend: process.env.ASR_BACKEND,
    whisperModel: process.env.WHISPER_MODEL,
  })

  try {
    // Process jobs in a loop
    while (true) {
      await processJobs(supabase)

      // Wait before checking for new jobs
      await new Promise((resolve) => setTimeout(resolve, 5000))
    }
  } catch (error) {
    logger.error({ msg: "Worker crashed", error })
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  logger.info("Worker shutting down")
  process.exit(0)
})

process.on("SIGTERM", () => {
  logger.info("Worker shutting down")
  process.exit(0)
})

// Start the worker
main()
