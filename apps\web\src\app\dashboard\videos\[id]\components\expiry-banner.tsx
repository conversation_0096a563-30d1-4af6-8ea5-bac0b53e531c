"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { Button } from "@reality-scripts/ui"

interface ExpiryBannerProps {
  videoId: string
  expiresAt: string | null
}

export default function ExpiryBanner({ videoId, expiresAt }: ExpiryBannerProps) {
  const [isExtending, setIsExtending] = useState(false)
  const router = useRouter()
  const supabase = createClientComponentClient()

  if (!expiresAt) return null

  const expiryDate = new Date(expiresAt)
  const hoursRemaining = Math.max(0, Math.floor((expiryDate.getTime() - Date.now()) / (1000 * 60 * 60)))

  const handleExtendTTL = async () => {
    try {
      setIsExtending(true)

      // Extend TTL by 72 hours from now
      const newExpiryDate = new Date(Date.now() + 72 * 60 * 60 * 1000)

      const { error } = await supabase
        .from("videos")
        .update({ expires_at: newExpiryDate.toISOString() })
        .eq("id", videoId)

      if (error) throw error

      // Refresh the page
      router.refresh()
    } catch (error) {
      console.error("Failed to extend TTL:", error)
      alert("Failed to extend video retention. Please try again.")
    } finally {
      setIsExtending(false)
    }
  }

  return (
    <div className="bg-amber-50 border-l-4 border-amber-400 p-4 mb-6 rounded">
      <div className="flex items-center justify-between">
        <div>
          <p className="font-medium text-amber-800">
            Video expires in {hoursRemaining} {hoursRemaining === 1 ? "hour" : "hours"}
          </p>
          <p className="text-sm text-amber-700">
            The original video will be deleted, but the transcript will remain available.
          </p>
        </div>
        <Button onClick={handleExtendTTL} isLoading={isExtending} disabled={isExtending}>
          Extend 72 Hours
        </Button>
      </div>
    </div>
  )
}
