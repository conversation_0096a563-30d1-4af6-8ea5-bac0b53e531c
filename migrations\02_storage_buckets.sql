-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('raw', 'raw', false, 1073741824, '{video/mp4,video/quicktime,audio/mpeg,audio/ogg}'),
  ('audio', 'audio', false, 104857600, '{audio/mpeg,audio/ogg,audio/wav}'),
  ('proxy', 'proxy', false, 104857600, '{video/mp4}'),
  ('exports', 'exports', false, 10485760, '{text/plain,application/json,text/vtt,text/srt}');

-- Drop existing policies
DROP POLICY IF EXISTS "Users can upload to raw bucket" ON storage.objects;
DROP POLICY IF EXISTS "Users can access their raw files" ON storage.objects;
DROP POLICY IF EXISTS "Users can access their audio files" ON storage.objects;
DROP POLICY IF EXISTS "Users can access their proxy files" ON storage.objects;
DROP POLICY IF EXISTS "Users can access their export files" ON storage.objects;

-- Enhanced storage policies with better security
CREATE POLICY "Users can upload to raw bucket"
  ON storage.objects FOR INSERT TO authenticated
  WITH CHECK (
    bucket_id = 'raw' 
    AND auth.uid()::text = (storage.foldername(name))[1]
    AND array_length(storage.foldername(name), 1) = 2
  );

CREATE POLICY "Users can access their raw files"
  ON storage.objects FOR SELECT TO authenticated
  USING (
    bucket_id = 'raw' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their raw files"
  ON storage.objects FOR DELETE TO authenticated
  USING (
    bucket_id = 'raw' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "System can manage audio files"
  ON storage.objects FOR ALL TO service_role
  USING (bucket_id = 'audio');

CREATE POLICY "Users can access their audio files"
  ON storage.objects FOR SELECT TO authenticated
  USING (
    bucket_id = 'audio' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "System can manage proxy files"
  ON storage.objects FOR ALL TO service_role
  USING (bucket_id = 'proxy');

CREATE POLICY "Users can access their proxy files"
  ON storage.objects FOR SELECT TO authenticated
  USING (
    bucket_id = 'proxy' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "System can manage export files"
  ON storage.objects FOR ALL TO service_role
  USING (bucket_id = 'exports');

CREATE POLICY "Users can access their export files"
  ON storage.objects FOR SELECT TO authenticated
  USING (
    bucket_id = 'exports' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Add file size and type validation
UPDATE storage.buckets 
SET 
  file_size_limit = 1073741824, -- 1GB
  allowed_mime_types = '{video/mp4,video/quicktime,video/avi,video/webm,audio/mpeg,audio/ogg,audio/wav,audio/mp3}'
WHERE id = 'raw';

UPDATE storage.buckets 
SET 
  file_size_limit = 104857600, -- 100MB
  allowed_mime_types = '{audio/mpeg,audio/ogg,audio/wav,audio/mp3}'
WHERE id = 'audio';

UPDATE storage.buckets 
SET 
  file_size_limit = 104857600, -- 100MB
  allowed_mime_types = '{video/mp4,video/webm}'
WHERE id = 'proxy';

UPDATE storage.buckets 
SET 
  file_size_limit = 10485760, -- 10MB
  allowed_mime_types = '{text/plain,application/json,text/vtt,application/x-subrip}'
WHERE id = 'exports';
