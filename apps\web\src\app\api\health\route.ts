import { NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { healthMonitor, createLogger } from "@reality-scripts/lib"

const logger = createLogger("health-api")

export async function GET() {
  try {
    logger.info("Health check requested")

    // Register Supabase health check
    const supabase = createRouteHandlerClient({ cookies })

    // Run all health checks
    const health = await healthMonitor.runAllChecks()

    const statusCode = health.status === "healthy" ? 200 : health.status === "degraded" ? 200 : 503

    logger.info(
      {
        status: health.status,
        checksCount: health.checks.length,
      },
      "Health check completed",
    )

    return NextResponse.json(health, { status: statusCode })
  } catch (error) {
    logger.error({ error }, "Health check failed")

    return NextResponse.json(
      {
        status: "unhealthy",
        checks: [],
        timestamp: new Date(),
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 503 },
    )
  }
}
