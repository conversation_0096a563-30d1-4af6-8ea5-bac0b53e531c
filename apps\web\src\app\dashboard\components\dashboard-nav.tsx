"use client"

import Link from "next/link"
import { useRouter, usePathname } from "next/navigation"
import { useState } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { LanguageSwitcher, useToast } from "@reality-scripts/ui"
import { createLogger, getLocaleDirection } from "@reality-scripts/lib"
import type { SupportedLocale } from "@reality-scripts/lib"
import { Menu, X, User, LogOut, Settings } from "lucide-react"

const logger = createLogger("dashboard-nav")

interface DashboardNavProps {
  user: any
  currentLocale: SupportedLocale
  messages: any
}

export default function DashboardNav({ user, currentLocale, messages }: DashboardNavProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()
  const supabase = createClientComponentClient()
  const { addToast } = useToast()
  const isRTL = getLocaleDirection(currentLocale) === "rtl"

  const handleSignOut = async () => {
    try {
      logger.info({ userId: user.id }, "User signing out")
      await supabase.auth.signOut()

      addToast({
        type: "success",
        title: messages.auth.logoutSuccess,
      })

      router.push("/")
      router.refresh()
    } catch (error) {
      logger.error({ error, userId: user.id }, "Sign out failed")
      addToast({
        type: "error",
        title: messages.common.error,
        message: "Failed to sign out. Please try again.",
      })
    }
  }

  const handleLocaleChange = (locale: SupportedLocale) => {
    logger.info({ oldLocale: currentLocale, newLocale: locale }, "Locale change requested")

    // Remove current locale from pathname and add new one
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}(-[A-Z]{2})?/, "") || "/"
    const newPath = locale === "en" ? pathWithoutLocale : `/${locale}${pathWithoutLocale}`

    router.push(newPath)
    router.refresh()
  }

  const navigationItems = [
    {
      name: messages.common.dashboard,
      href: `/dashboard`,
      current: pathname === "/dashboard" || pathname.endsWith("/dashboard"),
    },
    {
      name: messages.common.upload,
      href: `/dashboard/upload`,
      current: pathname.includes("/upload"),
    },
    {
      name: messages.common.videos,
      href: `/dashboard`,
      current: pathname === "/dashboard" || pathname.endsWith("/dashboard"),
    },
  ]

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200" dir={isRTL ? "rtl" : "ltr"}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and main navigation */}
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/dashboard" className="text-xl font-bold text-indigo-600">
                {messages.common.appName}
              </Link>
            </div>

            {/* Desktop navigation */}
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`
                    inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200
                    ${
                      item.current
                        ? "border-indigo-500 text-gray-900"
                        : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                    }
                  `}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>

          {/* Right side - Language switcher and user menu */}
          <div className="hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4">
            <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={handleLocaleChange} variant="compact" />

            {/* User menu */}
            <div className="relative">
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 p-2 hover:bg-gray-50 transition-colors duration-200"
                aria-expanded={isUserMenuOpen}
                aria-haspopup="true"
              >
                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-indigo-600" />
                </div>
                <span className="text-gray-700 font-medium">{user.email}</span>
              </button>

              {isUserMenuOpen && (
                <div
                  className={`
                  absolute ${isRTL ? "left-0" : "right-0"} mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50
                `}
                >
                  <div className="py-1">
                    <Link
                      href="/dashboard/settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <Settings className={`w-4 h-4 ${isRTL ? "ml-3" : "mr-3"}`} />
                      {messages.common.settings}
                    </Link>
                    <button
                      onClick={handleSignOut}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                    >
                      <LogOut className={`w-4 h-4 ${isRTL ? "ml-3" : "mr-3"}`} />
                      {messages.common.logout}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="sm:hidden flex items-center space-x-2">
            <LanguageSwitcher currentLocale={currentLocale} onLocaleChange={handleLocaleChange} variant="compact" />

            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
              aria-expanded={isMobileMenuOpen}
            >
              <span className="sr-only">Open main menu</span>
              {isMobileMenuOpen ? <X className="block h-6 w-6" /> : <Menu className="block h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="sm:hidden">
          <div className="pt-2 pb-3 space-y-1">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`
                  block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200
                  ${
                    item.current
                      ? "bg-indigo-50 border-indigo-500 text-indigo-700"
                      : "border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700"
                  }
                `}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}
          </div>

          <div className="pt-4 pb-3 border-t border-gray-200">
            <div className="flex items-center px-4">
              <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-indigo-600" />
              </div>
              <div className={`${isRTL ? "mr-3" : "ml-3"}`}>
                <div className="text-base font-medium text-gray-800">{user.email}</div>
              </div>
            </div>
            <div className="mt-3 space-y-1">
              <Link
                href="/dashboard/settings"
                className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition-colors duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {messages.common.settings}
              </Link>
              <button
                onClick={handleSignOut}
                className="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition-colors duration-200"
              >
                {messages.common.logout}
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  )
}
