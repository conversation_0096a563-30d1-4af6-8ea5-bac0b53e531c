import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { notFound } from "next/navigation"
import { VideoPlayer, TranscriptEditor } from "@reality-scripts/ui"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@reality-scripts/ui"
import VideoActions from "./components/video-actions"
import ExpiryBanner from "./components/expiry-banner"

export default async function VideoPage({ params }: { params: { id: string } }) {
  const supabase = createServerComponentClient({ cookies })

  // Get the video
  const { data: video, error: videoError } = await supabase.from("videos").select("*").eq("id", params.id).single()

  if (videoError || !video) {
    notFound()
  }

  // Get the transcription if available
  const { data: transcription, error: transcriptionError } = await supabase
    .from("transcriptions")
    .select("*")
    .eq("video_id", video.id)
    .single()

  // Get signed URL for the video
  let videoUrl = ""
  let vttUrl = ""

  if (video.status === "processed" || video.status === "transcribed") {
    // Try to get proxy URL first
    const { data: proxyData } = await supabase.storage
      .from("proxy")
      .createSignedUrl(video.storage_path.replace("raw/", ""), 3600)

    if (proxyData?.signedUrl) {
      videoUrl = proxyData.signedUrl
    } else {
      // Fall back to raw video
      const { data: rawData } = await supabase.storage
        .from("raw")
        .createSignedUrl(video.storage_path.replace("raw/", ""), 3600)

      if (rawData?.signedUrl) {
        videoUrl = rawData.signedUrl
      }
    }

    // Get VTT URL if transcription exists
    if (transcription) {
      const { data: vttData } = await supabase.storage
        .from("exports")
        .createSignedUrl(`${video.id}/transcript.vtt`, 3600)

      if (vttData?.signedUrl) {
        vttUrl = vttData.signedUrl
      }
    }
  }

  // Check if the video is about to expire
  const isExpiringSoon = video.expires_at && new Date(video.expires_at).getTime() - Date.now() < 12 * 60 * 60 * 1000 // 12 hours

  return (
    <div className="container mx-auto max-w-4xl">
      {isExpiringSoon && <ExpiryBanner videoId={video.id} expiresAt={video.expires_at} />}

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>{video.storage_path.split("/").pop()}</CardTitle>
        </CardHeader>
        <CardContent>
          {videoUrl ? (
            <VideoPlayer src={videoUrl} vttSrc={vttUrl} className="mb-4" />
          ) : (
            <div className="bg-gray-100 aspect-video flex items-center justify-center rounded-lg mb-4">
              <p className="text-gray-500">
                {video.status === "uploaded" ? "Processing video..." : "Video not available"}
              </p>
            </div>
          )}

          <VideoActions video={video} />
        </CardContent>
      </Card>

      {transcription ? (
        <Card>
          <CardHeader>
            <CardTitle>Transcript</CardTitle>
          </CardHeader>
          <CardContent>
            <TranscriptEditor
              words={transcription.words}
              onWordClick={(time) => {
                // This will be handled by client-side JavaScript
                const videoElement = document.querySelector("video")
                if (videoElement) {
                  videoElement.currentTime = time / 1000 // Convert ms to seconds
                  videoElement.play()
                }
              }}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-6 text-center">
            <p>{video.status === "uploaded" ? "Transcription in progress..." : "No transcription available"}</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
