"use client"

import React from "react"

/**
 * Performance Tests
 *
 * Tests to measure and validate performance characteristics
 * including load times, memory usage, and responsiveness.
 */

import { performance } from "perf_hooks"
import { screen, waitFor, fireEvent } from "@testing-library/react"
import { renderWithProviders, createMockFile } from "../utils/test-helpers"

// Mock performance observer
const mockPerformanceObserver = jest.fn()
global.PerformanceObserver = mockPerformanceObserver as any

describe("Performance Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks()
    performance.clearMarks()
    performance.clearMeasures()
  })

  describe("Component Rendering Performance", () => {
    it("should render dashboard within acceptable time", async () => {
      const startTime = performance.now()

      const DashboardPage = (await import("@/app/dashboard/page")).default
      renderWithProviders(<DashboardPage />)

      await waitFor(() => {
        expect(screen.getByText(/your videos/i)).toBeInTheDocument()
      })

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should render within 100ms
      expect(renderTime).toBeLessThan(100)
    })

    it("should handle large video lists efficiently", async () => {
      const startTime = performance.now()

      // Mock large dataset
      const largeVideoList = Array.from({ length: 1000 }, (_, i) => ({
        id: `video-${i}`,
        title: `Video ${i}`,
        status: "transcribed",
        created_at: new Date().toISOString(),
      }))

      const VideoList = ({ videos }: { videos: any[] }) => (
        <div>
          {videos.map((video) => (
            <div key={video.id} data-testid="video-item">
              {video.title}
            </div>
          ))}
        </div>
      )

      renderWithProviders(<VideoList videos={largeVideoList} />)

      await waitFor(() => {
        expect(screen.getAllByTestId("video-item")).toHaveLength(1000)
      })

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should handle large lists within 500ms
      expect(renderTime).toBeLessThan(500)
    })

    it("should virtualize long transcript efficiently", async () => {
      const startTime = performance.now()

      // Mock long transcript with 10,000 words
      const longTranscript = Array.from({ length: 10000 }, (_, i) => ({
        text: `Word${i}`,
        start: i * 100,
        end: (i + 1) * 100,
      }))

      const TranscriptEditor = (await import("@reality-scripts/ui")).TranscriptEditor
      renderWithProviders(<TranscriptEditor words={longTranscript} />)

      await waitFor(() => {
        expect(screen.getByText("Word0")).toBeInTheDocument()
      })

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should render efficiently even with large transcripts
      expect(renderTime).toBeLessThan(200)
    })

    it("should measure video player initialization time", async () => {
      performance.mark("video-player-start")

      const VideoPlayer = (await import("@reality-scripts/ui")).VideoPlayer
      renderWithProviders(<VideoPlayer src="https://example.com/video.mp4" poster="https://example.com/poster.jpg" />)

      await waitFor(() => {
        expect(document.querySelector("video")).toBeInTheDocument()
      })

      performance.mark("video-player-end")
      performance.measure("video-player-init", "video-player-start", "video-player-end")

      const measure = performance.getEntriesByName("video-player-init")[0]

      // Video player should initialize within 50ms
      expect(measure.duration).toBeLessThan(50)
    })
  })

  describe("Memory Usage Tests", () => {
    it("should not leak memory during component mounting/unmounting", async () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0

      // Mount and unmount components multiple times
      for (let i = 0; i < 100; i++) {
        const VideoPlayer = (await import("@reality-scripts/ui")).VideoPlayer
        const { unmount } = renderWithProviders(<VideoPlayer src="https://example.com/video.mp4" />)
        unmount()
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory

      // Memory increase should be minimal (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
    })

    it("should handle large file uploads without memory issues", async () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0

      // Create large mock file (100MB)
      const largeFile = createMockFile("large-video.mp4", "video/mp4", 100 * 1024 * 1024)

      const VideoUploader = (await import("@reality-scripts/ui")).VideoUploader
      renderWithProviders(<VideoUploader onUpload={jest.fn()} supportedLocales={[{ value: "en", label: "English" }]} />)

      // Simulate file selection
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, "files", {
        value: [largeFile],
        writable: false,
      })

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory

      // Memory increase should be reasonable
      expect(memoryIncrease).toBeLessThan(200 * 1024 * 1024) // Less than 200MB
    })
  })

  describe("Network Performance", () => {
    it("should measure API response times", async () => {
      const mockFetch = jest.fn().mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              ok: true,
              json: () => Promise.resolve({ videos: [] }),
            })
          }, 50) // Simulate 50ms response time
        })
      })

      global.fetch = mockFetch

      const startTime = performance.now()

      // Simulate API call
      const response = await fetch("/api/videos")
      const data = await response.json()

      const endTime = performance.now()
      const responseTime = endTime - startTime

      // API should respond within 100ms
      expect(responseTime).toBeLessThan(100)
      expect(data).toEqual({ videos: [] })
    })

    it("should handle concurrent API requests efficiently", async () => {
      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      })

      global.fetch = mockFetch

      const startTime = performance.now()

      // Make 10 concurrent requests
      const requests = Array.from({ length: 10 }, () => fetch("/api/videos"))

      await Promise.all(requests)

      const endTime = performance.now()
      const totalTime = endTime - startTime

      // Concurrent requests should complete efficiently
      expect(totalTime).toBeLessThan(200)
      expect(mockFetch).toHaveBeenCalledTimes(10)
    })

    it("should measure file upload performance", async () => {
      const mockFile = createMockFile("test-video.mp4", "video/mp4", 10 * 1024 * 1024) // 10MB

      const mockFetch = jest.fn().mockImplementation(() => {
        return new Promise((resolve) => {
          // Simulate upload time based on file size
          const uploadTime = (mockFile.size / (1024 * 1024)) * 10 // 10ms per MB
          setTimeout(() => {
            resolve({
              ok: true,
              json: () => Promise.resolve({ success: true }),
            })
          }, uploadTime)
        })
      })

      global.fetch = mockFetch

      const startTime = performance.now()

      // Simulate file upload
      const formData = new FormData()
      formData.append("file", mockFile)

      await fetch("/api/upload", {
        method: "POST",
        body: formData,
      })

      const endTime = performance.now()
      const uploadTime = endTime - startTime

      // Upload should complete within reasonable time
      expect(uploadTime).toBeLessThan(500) // 500ms for 10MB
    })
  })

  describe("Rendering Performance Optimization", () => {
    it("should use React.memo for expensive components", async () => {
      let renderCount = 0

      const ExpensiveComponent = React.memo(({ data }: { data: any[] }) => {
        renderCount++
        return (
          <div>
            {data.map((item) => (
              <div key={item.id}>{item.name}</div>
            ))}
          </div>
        )
      })

      const testData = [{ id: 1, name: "Test" }]

      const { rerender } = renderWithProviders(<ExpensiveComponent data={testData} />)

      // Re-render with same data
      rerender(<ExpensiveComponent data={testData} />)

      // Should only render once due to memoization
      expect(renderCount).toBe(1)
    })

    it("should debounce search input for performance", async () => {
      jest.useFakeTimers()

      let searchCallCount = 0
      const mockSearch = jest.fn(() => {
        searchCallCount++
      })

      const SearchComponent = () => {
        const [query, setQuery] = React.useState("")

        React.useEffect(() => {
          const debounced = setTimeout(() => {
            if (query) mockSearch(query)
          }, 300)

          return () => clearTimeout(debounced)
        }, [query])

        return <input value={query} onChange={(e) => setQuery(e.target.value)} placeholder="Search..." />
      }

      renderWithProviders(<SearchComponent />)

      const input = screen.getByPlaceholderText("Search...")

      // Type multiple characters quickly
      fireEvent.change(input, { target: { value: "h" } })
      fireEvent.change(input, { target: { value: "he" } })
      fireEvent.change(input, { target: { value: "hel" } })
      fireEvent.change(input, { target: { value: "hello" } })

      // Fast-forward time
      jest.advanceTimersByTime(300)

      // Should only call search once due to debouncing
      expect(searchCallCount).toBe(1)
      expect(mockSearch).toHaveBeenCalledWith("hello")

      jest.useRealTimers()
    })

    it("should lazy load components for better initial performance", async () => {
      const LazyComponent = React.lazy(() =>
        Promise.resolve({
          default: () => <div>Lazy loaded content</div>,
        }),
      )

      const startTime = performance.now()

      renderWithProviders(
        <React.Suspense fallback={<div>Loading...</div>}>
          <LazyComponent />
        </React.Suspense>,
      )

      // Should show loading state initially
      expect(screen.getByText("Loading...")).toBeInTheDocument()

      // Wait for lazy component to load
      await waitFor(() => {
        expect(screen.getByText("Lazy loaded content")).toBeInTheDocument()
      })

      const endTime = performance.now()
      const loadTime = endTime - startTime

      // Lazy loading should be fast
      expect(loadTime).toBeLessThan(50)
    })
  })

  describe("Bundle Size and Loading Performance", () => {
    it("should measure JavaScript bundle size impact", () => {
      // Mock bundle analyzer data
      const bundleStats = {
        "main.js": 250 * 1024, // 250KB
        "vendor.js": 500 * 1024, // 500KB
        "chunks.js": 100 * 1024, // 100KB
      }

      const totalSize = Object.values(bundleStats).reduce((sum, size) => sum + size, 0)

      // Total bundle should be under 1MB
      expect(totalSize).toBeLessThan(1024 * 1024)
    })

    it("should measure time to interactive", async () => {
      performance.mark("navigation-start")

      // Simulate page load
      const DashboardPage = (await import("@/app/dashboard/page")).default
      renderWithProviders(<DashboardPage />)

      await waitFor(() => {
        expect(screen.getByText(/your videos/i)).toBeInTheDocument()
      })

      performance.mark("interactive")
      performance.measure("time-to-interactive", "navigation-start", "interactive")

      const measure = performance.getEntriesByName("time-to-interactive")[0]

      // Time to interactive should be under 2 seconds
      expect(measure.duration).toBeLessThan(2000)
    })
  })

  describe("Database Query Performance", () => {
    it("should measure query execution time", async () => {
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          order: jest.fn().mockReturnThis(),
          limit: jest.fn().mockImplementation(() => {
            return new Promise((resolve) => {
              setTimeout(() => {
                resolve({
                  data: [],
                  error: null,
                })
              }, 25) // Simulate 25ms query time
            })
          }),
        })),
      }

      const startTime = performance.now()

      const result = await mockSupabase
        .from("videos")
        .select("*")
        .eq("user_id", "test-user")
        .order("created_at", { ascending: false })
        .limit(10)

      const endTime = performance.now()
      const queryTime = endTime - startTime

      // Database queries should be fast
      expect(queryTime).toBeLessThan(100)
      expect(result.data).toEqual([])
    })

    it("should handle pagination efficiently", async () => {
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn().mockReturnThis(),
          range: jest.fn().mockReturnThis(),
          order: jest.fn().mockImplementation(() => {
            return Promise.resolve({
              data: Array.from({ length: 20 }, (_, i) => ({ id: i })),
              error: null,
            })
          }),
        })),
      }

      const startTime = performance.now()

      // Load multiple pages
      const pages = await Promise.all([
        mockSupabase.from("videos").select("*").range(0, 19).order("created_at"),
        mockSupabase.from("videos").select("*").range(20, 39).order("created_at"),
        mockSupabase.from("videos").select("*").range(40, 59).order("created_at"),
      ])

      const endTime = performance.now()
      const totalTime = endTime - startTime

      // Pagination should be efficient
      expect(totalTime).toBeLessThan(200)
      expect(pages).toHaveLength(3)
    })
  })

  describe("Real User Metrics Simulation", () => {
    it("should measure First Contentful Paint (FCP)", async () => {
      performance.mark("fcp-start")

      const DashboardPage = (await import("@/app/dashboard/page")).default
      renderWithProviders(<DashboardPage />)

      // Wait for first content to appear
      await waitFor(() => {
        expect(screen.getByText(/realityscripts/i)).toBeInTheDocument()
      })

      performance.mark("fcp-end")
      performance.measure("first-contentful-paint", "fcp-start", "fcp-end")

      const fcp = performance.getEntriesByName("first-contentful-paint")[0]

      // FCP should be under 1.5 seconds
      expect(fcp.duration).toBeLessThan(1500)
    })

    it("should measure Largest Contentful Paint (LCP)", async () => {
      performance.mark("lcp-start")

      const VideoPage = (await import("@/app/dashboard/videos/[id]/page")).default
      renderWithProviders(<VideoPage />)

      // Wait for largest content (video player) to load
      await waitFor(() => {
        expect(document.querySelector("video")).toBeInTheDocument()
      })

      performance.mark("lcp-end")
      performance.measure("largest-contentful-paint", "lcp-start", "lcp-end")

      const lcp = performance.getEntriesByName("largest-contentful-paint")[0]

      // LCP should be under 2.5 seconds
      expect(lcp.duration).toBeLessThan(2500)
    })

    it("should measure Cumulative Layout Shift (CLS)", async () => {
      const layoutShiftScore = 0

      // Mock layout shift observer
      const mockObserver = {
        observe: jest.fn(),
        disconnect: jest.fn(),
      }

      global.PerformanceObserver = jest.fn().mockImplementation((callback) => {
        // Simulate layout shift entries
        setTimeout(() => {
          callback({
            getEntries: () => [
              { value: 0.05 }, // Small layout shift
              { value: 0.02 }, // Another small shift
            ],
          })
        }, 100)

        return mockObserver
      })

      const DashboardPage = (await import("@/app/dashboard/page")).default
      renderWithProviders(<DashboardPage />)

      await waitFor(() => {
        expect(screen.getByText(/your videos/i)).toBeInTheDocument()
      })

      // Wait for layout shift measurement
      await new Promise((resolve) => setTimeout(resolve, 150))

      // CLS should be minimal (under 0.1)
      expect(layoutShiftScore).toBeLessThan(0.1)
    })
  })
})
