import { type NextRequest, NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { z } from "zod"
import type { Database } from "@reality-scripts/lib"

// Validation schema
const uploadSchema = z.object({
  file: z.instanceof(File),
  locale: z.string().optional().default("en"),
})

// Allowed file types and sizes
const ALLOWED_VIDEO_TYPES = [
  "video/mp4",
  "video/webm",
  "video/quicktime",
  "video/x-msvideo",
  "audio/mpeg",
  "audio/wav",
  "audio/mp4",
]

const MAX_FILE_SIZE = 500 * 1024 * 1024 // 500MB

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check authentication
    const {
      data: { session },
      error: authError,
    } = await supabase.auth.getSession()

    if (authError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get("file") as File
    const locale = (formData.get("locale") as string) || "en"

    // Validate input
    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 })
    }

    if (!ALLOWED_VIDEO_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: "Invalid file type. Supported: MP4, WebM, MOV, AVI, MP3, WAV" },
        { status: 400 },
      )
    }

    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json({ error: "File too large. Maximum size: 500MB" }, { status: 400 })
    }

    // Generate unique file path
    const fileExtension = file.name.split(".").pop()
    const fileName = `${session.user.id}/${Date.now()}.${fileExtension}`

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage.from("raw").upload(fileName, file, {
      cacheControl: "3600",
      upsert: false,
    })

    if (uploadError) {
      console.error("Upload error:", uploadError)
      return NextResponse.json({ error: "Failed to upload file" }, { status: 500 })
    }

    // Create video record
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 48) // 48 hour TTL

    const { data: videoData, error: videoError } = await supabase
      .from("videos")
      .insert({
        user_id: session.user.id,
        storage_path: uploadData.path,
        locale,
        expires_at: expiresAt.toISOString(),
        status: "uploaded",
      })
      .select()
      .single()

    if (videoError) {
      console.error("Video creation error:", videoError)
      // Clean up uploaded file
      await supabase.storage.from("raw").remove([uploadData.path])
      return NextResponse.json({ error: "Failed to create video record" }, { status: 500 })
    }

    // Queue transcription job
    const { error: jobError } = await supabase.from("processing_jobs").insert({
      video_id: videoData.id,
      type: "transcribe",
      status: "queued",
      input: { locale, file_path: uploadData.path },
    })

    if (jobError) {
      console.error("Job creation error:", jobError)
    }

    return NextResponse.json({
      success: true,
      video: {
        id: videoData.id,
        status: videoData.status,
        expires_at: videoData.expires_at,
      },
    })
  } catch (error) {
    console.error("Upload API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 })
}
