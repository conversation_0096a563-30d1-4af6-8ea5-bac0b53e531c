import fs from "fs"
import path from "path"
import { tmpdir } from "os"
import { v4 as uuidv4 } from "uuid"
import type { SupabaseClient } from "@supabase/supabase-js"
import { logger } from "./logger"

// Download file from Supabase Storage
export async function downloadFile(supabase: SupabaseClient, storagePath: string): Promise<string> {
  try {
    const bucket = storagePath.split("/")[0]
    const filePath = storagePath.replace(`${bucket}/`, "")

    // Create temp directory if it doesn't exist
    const tempDir = path.join(tmpdir(), "reality-scripts")
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true })
    }

    // Generate a unique filename
    const tempFilePath = path.join(tempDir, `${uuidv4()}${path.extname(filePath)}`)

    // Download the file
    const { data, error } = await supabase.storage.from(bucket).download(filePath)

    if (error) {
      throw error
    }

    // Write the file to disk
    fs.writeFileSync(tempFilePath, Buffer.from(await data.arrayBuffer()))

    return tempFilePath
  } catch (error) {
    logger.error({ msg: "Failed to download file", storagePath, error })
    throw error
  }
}

// Upload file to Supabase Storage
export async function uploadFile(
  supabase: SupabaseClient,
  bucket: string,
  filePath: string,
  content: string,
  contentType: string,
): Promise<string> {
  try {
    // Upload the file
    const { error } = await supabase.storage.from(bucket).upload(filePath, content, {
      contentType,
      upsert: true,
    })

    if (error) {
      throw error
    }

    return `${bucket}/${filePath}`
  } catch (error) {
    logger.error({ msg: "Failed to upload file", bucket, filePath, error })
    throw error
  }
}

// Upload file from path to Supabase Storage
export async function uploadFileFromPath(
  supabase: SupabaseClient,
  bucket: string,
  filePath: string,
  localPath: string,
  contentType: string,
): Promise<string> {
  try {
    // Read the file
    const fileBuffer = fs.readFileSync(localPath)

    // Upload the file
    const { error } = await supabase.storage.from(bucket).upload(filePath, fileBuffer, {
      contentType,
      upsert: true,
    })

    if (error) {
      throw error
    }

    return `${bucket}/${filePath}`
  } catch (error) {
    logger.error({ msg: "Failed to upload file from path", bucket, filePath, localPath, error })
    throw error
  }
}
