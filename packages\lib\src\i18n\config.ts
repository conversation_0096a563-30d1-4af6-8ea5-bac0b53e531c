import type { LocaleConfig, SupportedLocale } from "./types"

export const LOCALE_CONFIGS: Record<SupportedLocale, LocaleConfig> = {
  en: {
    code: "en",
    name: "English",
    nativeName: "English",
    direction: "ltr",
    flag: "🇺🇸",
    whisperCode: "en",
  },
  es: {
    code: "es",
    name: "Spanish",
    nativeName: "Español",
    direction: "ltr",
    flag: "🇪🇸",
    whisperCode: "es",
  },
  fr: {
    code: "fr",
    name: "French",
    nativeName: "Français",
    direction: "ltr",
    flag: "🇫🇷",
    whisperCode: "fr",
  },
  de: {
    code: "de",
    name: "German",
    nativeName: "Deutsch",
    direction: "ltr",
    flag: "🇩🇪",
    whisperCode: "de",
  },
  it: {
    code: "it",
    name: "Italian",
    nativeName: "Italiano",
    direction: "ltr",
    flag: "🇮🇹",
    whisperCode: "it",
  },
  pt: {
    code: "pt",
    name: "Portuguese",
    nativeName: "Português",
    direction: "ltr",
    flag: "🇵🇹",
    whisperCode: "pt",
  },
  ru: {
    code: "ru",
    name: "Russian",
    nativeName: "Русский",
    direction: "ltr",
    flag: "🇷🇺",
    whisperCode: "ru",
  },
  zh: {
    code: "zh",
    name: "Chinese",
    nativeName: "中文",
    direction: "ltr",
    flag: "🇨🇳",
    whisperCode: "zh",
  },
  ja: {
    code: "ja",
    name: "Japanese",
    nativeName: "日本語",
    direction: "ltr",
    flag: "🇯🇵",
    whisperCode: "ja",
  },
  ko: {
    code: "ko",
    name: "Korean",
    nativeName: "한국어",
    direction: "ltr",
    flag: "🇰🇷",
    whisperCode: "ko",
  },
  ar: {
    code: "ar",
    name: "Arabic",
    nativeName: "العربية",
    direction: "rtl",
    flag: "🇸🇦",
    whisperCode: "ar",
  },
  hi: {
    code: "hi",
    name: "Hindi",
    nativeName: "हिन्दी",
    direction: "ltr",
    flag: "🇮🇳",
    whisperCode: "hi",
  },
}

export const DEFAULT_LOCALE: SupportedLocale = "en"

export const getSupportedLocales = () => Object.values(LOCALE_CONFIGS)

export const getLocaleConfig = (locale: SupportedLocale) => LOCALE_CONFIGS[locale]

export const isRTL = (locale: SupportedLocale) => LOCALE_CONFIGS[locale].direction === "rtl"

export const getWhisperCode = (locale: SupportedLocale) => LOCALE_CONFIGS[locale].whisperCode || locale
