version: '3'

services:
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
    depends_on:
      - worker

  worker:
    build:
      context: .
      dockerfile: apps/worker/Dockerfile
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - HF_API_TOKEN=${HF_API_TOKEN}
      - ASR_BACKEND=${ASR_BACKEND}
      - WHISPER_MODEL=${WHISPER_MODEL}
      - LOG_LEVEL=${LOG_LEVEL}
