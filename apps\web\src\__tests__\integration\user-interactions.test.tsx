import { screen, fireEvent, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { renderWithProviders, createMockFile } from "../utils/test-helpers"

// Mock Next.js router
const mockPush = jest.fn()
const mockRefresh = jest.fn()
const mockBack = jest.fn()

jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: mockPush,
    refresh: mockRefresh,
    back: mockBack,
  }),
  usePathname: () => "/dashboard",
  useSearchParams: () => new URLSearchParams(),
}))

// Import components after mocking
import DashboardPage from "@/app/dashboard/page"
import UploadPage from "@/app/dashboard/upload/page"
import VideoPage from "@/app/dashboard/videos/[id]/page"
import LoginPage from "@/app/login/page"
import TranscriptEditor from "@/components/transcript-editor"

describe("User Interaction Workflows", () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe("Authentication Flow", () => {
    it("should handle complete login workflow", async () => {
      const user = userEvent.setup()

      renderWithProviders(<LoginPage />)

      // User sees login form
      expect(screen.getByRole("heading", { name: /login/i })).toBeInTheDocument()

      // User fills in email
      const emailInput = screen.getByLabelText(/email/i)
      await user.type(emailInput, "<EMAIL>")
      expect(emailInput).toHaveValue("<EMAIL>")

      // User fills in password
      const passwordInput = screen.getByLabelText(/password/i)
      await user.type(passwordInput, "password123")
      expect(passwordInput).toHaveValue("password123")

      // User submits form
      const submitButton = screen.getByRole("button", { name: /login/i })
      await user.click(submitButton)

      // Form should show loading state
      expect(screen.getByText(/logging in/i)).toBeInTheDocument()

      // After successful login, should redirect
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/dashboard")
      })
    })

    it("should handle login errors gracefully", async () => {
      const user = userEvent.setup()

      // Mock failed login
      const mockSupabase = {
        auth: {
          signInWithPassword: jest.fn().mockResolvedValue({
            data: { session: null },
            error: { message: "Invalid credentials" },
          }),
        },
      }

      renderWithProviders(<LoginPage />)

      // Fill form with invalid credentials
      await user.type(screen.getByLabelText(/email/i), "<EMAIL>")
      await user.type(screen.getByLabelText(/password/i), "wrongpassword")
      await user.click(screen.getByRole("button", { name: /login/i }))

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument()
      })

      // Form should remain accessible
      expect(screen.getByLabelText(/email/i)).toBeEnabled()
      expect(screen.getByLabelText(/password/i)).toBeEnabled()
    })

    it("should validate form inputs", async () => {
      const user = userEvent.setup()

      renderWithProviders(<LoginPage />)

      // Try to submit empty form
      const submitButton = screen.getByRole("button", { name: /login/i })
      await user.click(submitButton)

      // Should show validation errors
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()

      // Fill invalid email
      await user.type(screen.getByLabelText(/email/i), "invalid-email")
      await user.click(submitButton)

      expect(screen.getByText(/invalid email format/i)).toBeInTheDocument()
    })
  })

  describe("Video Upload Workflow", () => {
    it("should handle complete upload process", async () => {
      const user = userEvent.setup()
      const mockOnUpload = jest.fn().mockResolvedValue(undefined)

      renderWithProviders(<UploadPage onUpload={mockOnUpload} />)

      // User sees upload interface
      expect(screen.getByText(/upload video or audio/i)).toBeInTheDocument()

      // User selects a file
      const fileInput = screen.getByLabelText(/select file/i)
      const testFile = createMockFile("test-video.mp4", "video/mp4")

      await user.upload(fileInput, testFile)

      // File should be displayed
      await waitFor(() => {
        expect(screen.getByText("test-video.mp4")).toBeInTheDocument()
      })

      // User selects language
      const languageSelect = screen.getByLabelText(/language/i)
      await user.selectOptions(languageSelect, "en")

      // User clicks upload
      const uploadButton = screen.getByRole("button", { name: /upload file/i })
      await user.click(uploadButton)

      // Should show upload progress
      expect(screen.getByText(/uploading/i)).toBeInTheDocument()

      // Should call upload function
      expect(mockOnUpload).toHaveBeenCalledWith(testFile, "en")

      // Should show success message
      await waitFor(() => {
        expect(screen.getByText(/upload successful/i)).toBeInTheDocument()
      })
    })

    it("should handle drag and drop upload", async () => {
      const user = userEvent.setup()

      renderWithProviders(<UploadPage />)

      const dropZone = screen.getByText(/drag and drop/i).closest("div")
      const testFile = createMockFile("test-video.mp4", "video/mp4")

      // Simulate drag enter
      fireEvent.dragEnter(dropZone!, {
        dataTransfer: {
          files: [testFile],
          types: ["Files"],
        },
      })

      // Drop zone should show active state
      expect(dropZone).toHaveClass("border-indigo-500")

      // Simulate drop
      fireEvent.drop(dropZone!, {
        dataTransfer: {
          files: [testFile],
        },
      })

      // File should be selected
      await waitFor(() => {
        expect(screen.getByText("test-video.mp4")).toBeInTheDocument()
      })
    })

    it("should validate file types and sizes", async () => {
      const user = userEvent.setup()

      renderWithProviders(<UploadPage />)

      // Try to upload invalid file type
      const fileInput = screen.getByLabelText(/select file/i)
      const invalidFile = createMockFile("document.pdf", "application/pdf")

      await user.upload(fileInput, invalidFile)

      // Should show error
      await waitFor(() => {
        expect(screen.getByText(/file type.*not supported/i)).toBeInTheDocument()
      })

      // Try to upload oversized file
      const largeFile = createMockFile("large-video.mp4", "video/mp4", 2 * 1024 * 1024 * 1024) // 2GB

      await user.upload(fileInput, largeFile)

      await waitFor(() => {
        expect(screen.getByText(/file size exceeds/i)).toBeInTheDocument()
      })
    })

    it("should allow file removal and re-selection", async () => {
      const user = userEvent.setup()

      renderWithProviders(<UploadPage />)

      // Upload a file
      const fileInput = screen.getByLabelText(/select file/i)
      const testFile = createMockFile("test-video.mp4", "video/mp4")

      await user.upload(fileInput, testFile)

      // File should be displayed
      await waitFor(() => {
        expect(screen.getByText("test-video.mp4")).toBeInTheDocument()
      })

      // Remove the file
      const removeButton = screen.getByRole("button", { name: /remove/i })
      await user.click(removeButton)

      // File should be removed
      expect(screen.queryByText("test-video.mp4")).not.toBeInTheDocument()

      // Upload interface should be restored
      expect(screen.getByText(/drag and drop/i)).toBeInTheDocument()
    })
  })

  describe("Video Playback and Transcript Interaction", () => {
    it("should handle video playback controls", async () => {
      const user = userEvent.setup()

      const mockVideoData = {
        id: "video-1",
        storage_path: "raw/user/video.mp4",
        status: "transcribed",
      }

      const mockTranscription = {
        words: [
          { text: "Hello", start: 0, end: 500 },
          { text: "world", start: 500, end: 1000 },
        ],
      }

      renderWithProviders(<VideoPage video={mockVideoData} transcription={mockTranscription} />)

      // Video player should be present
      const video = document.querySelector("video")
      expect(video).toBeInTheDocument()

      // Play button should be present
      const playButton = screen.getByRole("button", { name: /play/i })
      expect(playButton).toBeInTheDocument()

      // Click play button
      await user.click(playButton)

      // Should call video play method
      expect(HTMLMediaElement.prototype.play).toHaveBeenCalled()

      // Button should change to pause
      await waitFor(() => {
        expect(screen.getByRole("button", { name: /pause/i })).toBeInTheDocument()
      })
    })

    it("should handle transcript word clicking", async () => {
      const user = userEvent.setup()

      const mockTranscription = {
        words: [
          { text: "Hello", start: 0, end: 500 },
          { text: "world", start: 500, end: 1000 },
          { text: "This", start: 1500, end: 2000 },
          { text: "is", start: 2000, end: 2250 },
          { text: "a", start: 2250, end: 2400 },
          { text: "test", start: 2400, end: 2800 },
        ],
      }

      renderWithProviders(<TranscriptEditor words={mockTranscription.words} onWordClick={jest.fn()} />)

      // Words should be clickable
      const helloWord = screen.getByText("Hello")
      expect(helloWord).toBeInTheDocument()

      // Click on a word
      await user.click(helloWord)

      // Should highlight the word
      expect(helloWord).toHaveClass("bg-primary")
    })

    it("should synchronize video and transcript", async () => {
      const user = userEvent.setup()

      const mockTranscription = {
        words: [
          { text: "Hello", start: 0, end: 500 },
          { text: "world", start: 500, end: 1000 },
        ],
      }

      const { rerender } = renderWithProviders(<TranscriptEditor words={mockTranscription.words} currentTime={0} />)

      // First word should be highlighted initially
      expect(screen.getByText("Hello")).toHaveClass("bg-primary")

      // Update current time
      rerender(
        <TranscriptEditor
          words={mockTranscription.words}
          currentTime={750} // Should highlight "world"
        />,
      )

      // Second word should now be highlighted
      expect(screen.getByText("world")).toHaveClass("bg-primary")
      expect(screen.getByText("Hello")).not.toHaveClass("bg-primary")
    })
  })

  describe("Navigation and Routing", () => {
    it("should handle navigation between pages", async () => {
      const user = userEvent.setup()

      renderWithProviders(<DashboardPage />)

      // Should show dashboard content
      expect(screen.getByText(/your videos/i)).toBeInTheDocument()

      // Click upload button
      const uploadButton = screen.getByRole("link", { name: /upload new/i })
      await user.click(uploadButton)

      // Should navigate to upload page
      expect(mockPush).toHaveBeenCalledWith("/dashboard/upload")
    })

    it("should handle browser back button", async () => {
      const user = userEvent.setup()

      renderWithProviders(<VideoPage />)

      // Simulate back button click
      fireEvent.popstate(window)

      // Should call router back
      expect(mockBack).toHaveBeenCalled()
    })

    it("should handle deep linking", () => {
      // Mock URL with video ID
      Object.defineProperty(window, "location", {
        value: {
          pathname: "/dashboard/videos/video-123",
          search: "?t=30",
        },
        writable: true,
      })

      renderWithProviders(<VideoPage />)

      // Should load video with timestamp
      expect(screen.getByText(/video/i)).toBeInTheDocument()
    })
  })

  describe("Form Interactions", () => {
    it("should handle complex form validation", async () => {
      const user = userEvent.setup()

      renderWithProviders(<UploadPage />)

      // Fill form with various inputs
      const titleInput = screen.getByLabelText(/title/i)
      await user.type(titleInput, "My Test Video")

      const descriptionInput = screen.getByLabelText(/description/i)
      await user.type(descriptionInput, "This is a test video for the platform")

      const languageSelect = screen.getByLabelText(/language/i)
      await user.selectOptions(languageSelect, "es")

      // Verify form state
      expect(titleInput).toHaveValue("My Test Video")
      expect(descriptionInput).toHaveValue("This is a test video for the platform")
      expect(languageSelect).toHaveValue("es")
    })

    it("should handle form reset", async () => {
      const user = userEvent.setup()

      renderWithProviders(<UploadPage />)

      // Fill form
      const titleInput = screen.getByLabelText(/title/i)
      await user.type(titleInput, "Test Title")

      // Reset form
      const resetButton = screen.getByRole("button", { name: /reset/i })
      await user.click(resetButton)

      // Form should be cleared
      expect(titleInput).toHaveValue("")
    })

    it("should handle auto-save functionality", async () => {
      const user = userEvent.setup()

      renderWithProviders(<UploadPage />)

      const titleInput = screen.getByLabelText(/title/i)
      await user.type(titleInput, "Auto-saved Title")

      // Should auto-save after delay
      await waitFor(
        () => {
          expect(screen.getByText(/draft saved/i)).toBeInTheDocument()
        },
        { timeout: 3000 },
      )
    })
  })

  describe("Keyboard Navigation", () => {
    it("should support keyboard navigation", async () => {
      const user = userEvent.setup()

      renderWithProviders(<DashboardPage />)

      // Tab through interactive elements
      await user.tab()
      expect(document.activeElement).toHaveAttribute("role", "button")

      await user.tab()
      expect(document.activeElement).toHaveAttribute("role", "link")

      // Enter should activate focused element
      await user.keyboard("{Enter}")
      expect(mockPush).toHaveBeenCalled()
    })

    it("should handle keyboard shortcuts", async () => {
      const user = userEvent.setup()

      renderWithProviders(<VideoPage />)

      // Space should play/pause video
      await user.keyboard(" ")
      expect(HTMLMediaElement.prototype.play).toHaveBeenCalled()

      // Arrow keys should seek
      await user.keyboard("{ArrowRight}")
      // Should seek forward

      await user.keyboard("{ArrowLeft}")
      // Should seek backward
    })
  })

  describe("Touch and Mobile Interactions", () => {
    beforeEach(() => {
      // Mock mobile viewport
      Object.defineProperty(window, "innerWidth", {
        writable: true,
        configurable: true,
        value: 375,
      })
    })

    it("should handle touch gestures", async () => {
      renderWithProviders(<VideoPage />)

      const video = document.querySelector("video")

      // Simulate touch events
      fireEvent.touchStart(video!, {
        touches: [{ clientX: 100, clientY: 100 }],
      })

      fireEvent.touchMove(video!, {
        touches: [{ clientX: 200, clientY: 100 }],
      })

      fireEvent.touchEnd(video!)

      // Should handle swipe gesture
      expect(video).toBeInTheDocument()
    })

    it("should adapt UI for mobile", () => {
      renderWithProviders(<DashboardPage />)

      // Mobile-specific elements should be visible
      expect(screen.getByRole("button", { name: /menu/i })).toBeInTheDocument()
    })
  })

  describe("Error Recovery", () => {
    it("should handle network errors gracefully", async () => {
      const user = userEvent.setup()

      // Mock network failure
      const mockFetch = jest.fn().mockRejectedValue(new Error("Network error"))
      global.fetch = mockFetch

      renderWithProviders(<UploadPage />)

      // Try to upload
      const fileInput = screen.getByLabelText(/select file/i)
      const testFile = createMockFile("test-video.mp4", "video/mp4")

      await user.upload(fileInput, testFile)
      await user.click(screen.getByRole("button", { name: /upload/i }))

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument()
      })

      // Should allow retry
      const retryButton = screen.getByRole("button", { name: /retry/i })
      expect(retryButton).toBeInTheDocument()
    })

    it("should handle session expiration", async () => {
      const user = userEvent.setup()

      // Mock expired session
      const mockSupabase = {
        auth: {
          getSession: jest.fn().mockResolvedValue({
            data: { session: null },
            error: { message: "Session expired" },
          }),
        },
      }

      renderWithProviders(<DashboardPage />)

      // Should redirect to login
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/login")
      })
    })
  })
})
