/**
 * Test Utilities and Helpers
 *
 * Common utilities for setting up test environments, mocking data,
 * and providing reusable test functions across the test suite.
 */

import { render, type RenderOptions } from "@testing-library/react"
import { NextIntlClientProvider } from "next-intl"
import { ToastProvider } from "@reality-scripts/ui"
import type { SupportedLocale } from "@reality-scripts/lib"
import type React from "react"

// Mock messages for testing
export const mockMessages = {
  common: {
    appName: "RealityScripts",
    login: "Login",
    register: "Register",
    logout: "Logout",
    loading: "Loading...",
    error: "Error",
    success: "Success",
  },
  dashboard: {
    yourVideos: "Your Videos",
    uploadNew: "Upload New",
    noVideos: "You haven't uploaded any videos yet.",
  },
  upload: {
    title: "Upload Video or Audio",
    dragDrop: "Drag and drop your file here",
    selectFile: "Select File",
    uploading: "Uploading...",
    uploadSuccess: "Upload Successful",
    uploadError: "Upload Failed",
  },
  video: {
    transcript: "Transcript",
    clickToSeek: "Click to seek",
  },
  errors: {
    uploadFailed: "Upload failed. Please try again.",
    unauthorized: "Unauthorized. Please login.",
  },
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  locale?: SupportedLocale
  messages?: any
}

export function renderWithProviders(
  ui: React.ReactElement,
  { locale = "en", messages = mockMessages, ...renderOptions }: CustomRenderOptions = {},
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <NextIntlClientProvider locale={locale} messages={messages}>
        <ToastProvider>{children}</ToastProvider>
      </NextIntlClientProvider>
    )
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock Supabase client factory
export function createMockSupabaseClient() {
  return {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: global.testUtils.mockUserSession },
        error: null,
      }),
      getUser: jest.fn().mockResolvedValue({
        data: { user: global.testUtils.mockUserSession.user },
        error: null,
      }),
      signInWithPassword: jest.fn().mockResolvedValue({
        data: { session: global.testUtils.mockUserSession },
        error: null,
      }),
      signUp: jest.fn().mockResolvedValue({
        data: { user: global.testUtils.mockUserSession.user },
        error: null,
      }),
      signOut: jest.fn().mockResolvedValue({ error: null }),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({
        data: global.testUtils.mockVideoData,
        error: null,
      }),
    })),
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn().mockResolvedValue({
          data: { path: "test-path" },
          error: null,
        }),
        download: jest.fn().mockResolvedValue({
          data: new Blob(["test content"]),
          error: null,
        }),
        createSignedUrl: jest.fn().mockResolvedValue({
          data: { signedUrl: "https://example.com/signed-url" },
          error: null,
        }),
      })),
    },
  }
}

// File creation helpers for testing
export function createMockFile(
  name = "test-video.mp4",
  type = "video/mp4",
  size: number = 1024 * 1024, // 1MB
): File {
  const content = new Array(size).fill("a").join("")
  return new File([content], name, { type })
}

export function createMockAudioFile(
  name = "test-audio.mp3",
  size: number = 512 * 1024, // 512KB
): File {
  return createMockFile(name, "audio/mpeg", size)
}

// Wait for async operations in tests
export function waitForAsync(ms = 0): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

// Mock intersection observer for testing
export function mockIntersectionObserver() {
  const mockIntersectionObserver = jest.fn()
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  // @ts-expect-error - IntersectionObserver is not defined in the global scope
  window.IntersectionObserver = mockIntersectionObserver
}

// Mock resize observer for testing
export function mockResizeObserver() {
  const mockResizeObserver = jest.fn()
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  // @ts-expect-error - ResizeObserver is not defined in the global scope
  window.ResizeObserver = mockResizeObserver
}

// Viewport size helpers for responsive testing
export function setMobileViewport() {
  Object.defineProperty(window, "innerWidth", {
    writable: true,
    configurable: true,
    value: 375,
  })
  Object.defineProperty(window, "innerHeight", {
    writable: true,
    configurable: true,
    value: 667,
  })
}

export function setTabletViewport() {
  Object.defineProperty(window, "innerWidth", {
    writable: true,
    configurable: true,
    value: 768,
  })
  Object.defineProperty(window, "innerHeight", {
    writable: true,
    configurable: true,
    value: 1024,
  })
}

export function setDesktopViewport() {
  Object.defineProperty(window, "innerWidth", {
    writable: true,
    configurable: true,
    value: 1920,
  })
  Object.defineProperty(window, "innerHeight", {
    writable: true,
    configurable: true,
    value: 1080,
  })
}

// Custom matchers for better assertions
export const customMatchers = {
  toBeValidVideo(received: File) {
    const validTypes = ["video/mp4", "video/quicktime", "video/x-msvideo"]
    const pass = validTypes.includes(received.type)

    return {
      message: () => `expected ${received.type} ${pass ? "not " : ""}to be a valid video type`,
      pass,
    }
  },

  toBeValidAudio(received: File) {
    const validTypes = ["audio/mpeg", "audio/ogg", "audio/wav"]
    const pass = validTypes.includes(received.type)

    return {
      message: () => `expected ${received.type} ${pass ? "not " : ""}to be a valid audio type`,
      pass,
    }
  },
}

// Error simulation helpers
export function simulateNetworkError() {
  return jest.fn().mockRejectedValue(new Error("Network error"))
}

export function simulateAuthError() {
  return jest.fn().mockRejectedValue(new Error("Authentication failed"))
}

export function simulateValidationError() {
  return jest.fn().mockRejectedValue(new Error("Validation failed"))
}
