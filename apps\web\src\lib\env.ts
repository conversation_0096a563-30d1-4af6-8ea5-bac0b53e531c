/**
 * Environment configuration with comprehensive validation
 * Ensures all required environment variables are present and valid
 */

import { z } from "zod"

// Define environment schema
const envSchema = z.object({
  // Supabase configuration
  NEXT_PUBLIC_SUPABASE_URL: z.string().url("Invalid Supabase URL"),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1, "Supabase anon key is required"),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1, "Supabase service role key is required"),
  SUPABASE_JWT_SECRET: z.string().optional(),

  // ASR configuration
  HF_API_TOKEN: z.string().optional(),
  WHISPER_MODEL: z.enum(["tiny", "base", "small", "medium", "large", "large-v2", "large-v3"]).default("small"),
  ASR_BACKEND: z.enum(["hf", "local"]).default("hf"),

  // Application configuration
  LOG_LEVEL: z.enum(["debug", "info", "warn", "error"]).default("info"),
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),

  // Optional features
  ENABLE_ANALYTICS: z
    .string()
    .transform((val) => val === "true")
    .default("false"),
  ENABLE_MONITORING: z
    .string()
    .transform((val) => val === "true")
    .default("false"),
})

// Validate environment variables
function validateEnv() {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map((err) => `${err.path.join(".")}: ${err.message}`).join("\n")
      throw new Error(`Environment validation failed:\n${missingVars}`)
    }
    throw error
  }
}

// Export validated environment
export const env = validateEnv()

// Validate ASR backend configuration
if (env.ASR_BACKEND === "hf" && !env.HF_API_TOKEN) {
  throw new Error("HF_API_TOKEN is required when ASR_BACKEND is 'hf'")
}

// Type-safe environment access
export type Environment = typeof env

// Development logging
if (env.NODE_ENV === "development") {
  console.log("✅ Environment configuration loaded:", {
    SUPABASE_URL: env.NEXT_PUBLIC_SUPABASE_URL,
    ASR_BACKEND: env.ASR_BACKEND,
    WHISPER_MODEL: env.WHISPER_MODEL,
    LOG_LEVEL: env.LOG_LEVEL,
    ANALYTICS: env.ENABLE_ANALYTICS,
    MONITORING: env.ENABLE_MONITORING,
  })
}
