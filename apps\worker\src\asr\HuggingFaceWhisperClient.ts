import fs from "fs"
import { v4 as uuidv4 } from "uuid"
import fetch from "node-fetch"
import type { AsrClient, TranscriptionResult } from "./AsrClient"
import { logger } from "../utils/logger"

export class HuggingFaceWhisperClient implements AsrClient {
  constructor(
    private apiToken: string,
    private model = "openai/whisper-large-v3",
  ) {}

  async transcribe(filePath: string, locale?: string): Promise<TranscriptionResult> {
    logger.info({ msg: "Transcribing with Hugging Face", model: this.model, locale })

    try {
      const fileBuffer = fs.readFileSync(filePath)

      // Prepare form data
      const formData = new FormData()
      const blob = new Blob([fileBuffer])
      formData.append("file", blob, "audio.mp3")

      if (locale && locale !== "auto") {
        formData.append("language", locale)
      }

      // Set task to transcribe with word timestamps
      formData.append("task", "transcribe")
      formData.append("return_timestamps", "word")

      // Call Hugging Face Inference API
      const response = await fetch(`https://api-inference.huggingface.co/models/${this.model}`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiToken}`,
        },
        body: formData,
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Hugging Face API error: ${response.status} ${errorText}`)
      }

      const result = await response.json()

      // Transform the result to our standard format
      const transcriptionId = uuidv4()
      const words = result.chunks.map((chunk: any) => ({
        text: chunk.text,
        start: Math.round(chunk.timestamp[0] * 1000), // convert to ms
        end: Math.round(chunk.timestamp[1] * 1000), // convert to ms
        confidence: chunk.confidence || 1.0,
      }))

      return {
        id: transcriptionId,
        text: result.text,
        language: result.language || locale || "en",
        words,
        raw: result,
        duration: words.length > 0 ? words[words.length - 1].end : 0,
      }
    } catch (error) {
      logger.error({ msg: "Hugging Face transcription failed", error })
      throw error
    }
  }
}
