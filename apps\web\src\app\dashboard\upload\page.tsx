"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { VideoUploader } from "@reality-scripts/ui"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@reality-scripts/ui"
import { supportedLocales } from "@/lib/i18n"

export default function UploadPage() {
  const [isUploading, setIsUploading] = useState(false)
  const router = useRouter()
  const supabase = createClientComponentClient()

  const handleUpload = async (file: File, locale: string) => {
    try {
      setIsUploading(true)

      // Get current user
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (!user) throw new Error("User not authenticated")

      // Create a unique file path
      const fileExt = file.name.split(".").pop()
      const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`
      const filePath = `${user.id}/${fileName}`

      // Upload the file to Supabase Storage
      const { error: uploadError } = await supabase.storage.from("raw").upload(filePath, file)

      if (uploadError) throw uploadError

      // Create a record in the videos table
      const { error: dbError } = await supabase.from("videos").insert({
        user_id: user.id,
        storage_path: `raw/${filePath}`,
        locale: locale === "auto" ? "en" : locale, // Default to 'en' if auto-detect
        expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000).toISOString(), // 72 hours from now
        status: "uploaded",
      })

      if (dbError) throw dbError

      // Redirect to dashboard
      router.push("/dashboard")
      router.refresh()
    } catch (error) {
      console.error("Upload error:", error)
      alert("Upload failed. Please try again.")
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="container mx-auto max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle className="text-center">Upload Video or Audio</CardTitle>
        </CardHeader>
        <CardContent>
          <VideoUploader onUpload={handleUpload} supportedLocales={supportedLocales} isUploading={isUploading} />
          <div className="mt-6 text-sm text-gray-500">
            <p className="mb-2">Note:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>Supported formats: .mp3, .ogg, .mp4, .mov</li>
              <li>Maximum file size: 1GB</li>
              <li>Videos will be automatically deleted after 72 hours unless extended</li>
              <li>Transcripts and low-resolution proxies will be preserved</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
