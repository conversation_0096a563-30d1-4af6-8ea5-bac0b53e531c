"use client"

import { TranscriptEditor } from "@reality-scripts/ui"

export default function SyntheticV0PageForDeployment() {
  // Sample data for demonstration
  const sampleWords = [
    { text: "Welcome", start: 0, end: 500, confidence: 0.95 },
    { text: "to", start: 500, end: 750, confidence: 0.98 },
    { text: "RealityScripts", start: 750, end: 1500, confidence: 0.92 },
    { text: "transcript", start: 1500, end: 2000, confidence: 0.96 },
    { text: "editor", start: 2000, end: 2500, confidence: 0.94 },
    { text: "demo", start: 2500, end: 3000, confidence: 0.97 }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            RealityScripts Demo
          </h1>
          <p className="text-lg text-gray-600">
            Interactive transcript editor with clickable words
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Sample Transcript</h2>
          <TranscriptEditor
            words={sampleWords}
            onWordClick={(time) => {
              console.log(`Clicked word at time: ${time}ms`)
            }}
          />
        </div>
      </div>
    </div>
  )
}