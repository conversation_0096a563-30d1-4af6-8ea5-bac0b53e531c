/**
 * Language Switching End-to-End Tests
 *
 * Tests the complete language switching functionality including
 * UI updates, RTL support, and persistence across navigation.
 */

import { test, expect } from "@playwright/test"

test.describe("Language Switching", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/")
  })

  test("should switch language and update UI", async ({ page }) => {
    // Open language switcher
    await page.click('[aria-label="Select language"]')

    // Select Spanish
    await page.click("text=Español")

    // Verify UI is updated to Spanish
    await expect(page.locator("text=Iniciar sesión")).toBeVisible()
    await expect(page.locator("text=Registrarse")).toBeVisible()
  })

  test("should handle Arabic RTL layout correctly", async ({ page }) => {
    // Switch to Arabic
    await page.click('[aria-label="Select language"]')
    await page.click("text=العربية")

    // Verify RTL direction is applied
    const body = page.locator("body")
    await expect(body).toHaveAttribute("dir", "rtl")

    // Verify Arabic text is displayed
    await expect(page.locator("text=تسجيل الدخول")).toBeVisible()
  })

  test("should persist language selection across navigation", async ({ page }) => {
    // Switch to Spanish
    await page.click('[aria-label="Select language"]')
    await page.click("text=Español")

    // Navigate to login page
    await page.click("text=Iniciar sesión")

    // Verify Spanish is still active
    await expect(page.locator("text=Iniciar sesión en RealityScripts")).toBeVisible()
  })

  test("should handle language switching in forms", async ({ page }) => {
    // Navigate to login page
    await page.click("text=Login")

    // Switch to French
    await page.click('[aria-label="Select language"]')
    await page.click("text=Français")

    // Verify form labels are translated
    await expect(page.locator('label:has-text("E-mail")')).toBeVisible()
    await expect(page.locator('label:has-text("Mot de passe")')).toBeVisible()
  })

  test("should maintain accessibility in different languages", async ({ page }) => {
    // Test keyboard navigation in Arabic (RTL)
    await page.click('[aria-label="Select language"]')
    await page.click("text=العربية")

    // Verify keyboard navigation works in RTL
    await page.keyboard.press("Tab")
    const focusedElement = page.locator(":focus")
    await expect(focusedElement).toBeVisible()
  })
})
